<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.1</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.kering.cus.qa</groupId>
	<artifactId>qa-test</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<name>unity test</name>
	<description>unity test project</description>
	<properties>
		<java.version>21</java.version>
		<jacoco-plugin.version>0.8.12</jacoco-plugin.version>
		<sonar-plugin.version>3.10.0.2594</sonar-plugin.version>
		<spotbugs-plugin.version>*******</spotbugs-plugin.version>
		<findsecbugs-plugin.version>1.12.0</findsecbugs-plugin.version>
		<cus-lib.version>1.34.0</cus-lib.version>
		<!--<private-maven.snapshot>https://artifactory-master.arsenal.keringcn-syss.net/artifactory/keringtech-maven-snapshot-local-cus-alicloud-master</private-maven.snapshot>
		<private-maven.release>https://artifactory-master.arsenal.keringcn-syss.net/artifactory/keringtech-maven-local-cus-alicloud-master</private-maven.release>-->
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib</artifactId>
				<version>${cus-lib.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>
			<version>5.4.4</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.springframework.boot</groupId>-->
<!--			<artifactId>spring-boot-devtools</artifactId>-->
<!--			<scope>runtime</scope>-->
<!--			<optional>true</optional>-->
<!--		</dependency>-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>


        <dependency>
            <groupId>com.kering.cus.lib</groupId>
            <artifactId>cus-lib-secret-access-support</artifactId>
            <version>${cus-lib.version}</version>
        </dependency>
        <dependency>
            <groupId>com.kering.cus.lib</groupId>
            <artifactId>cus-lib</artifactId>
            <type>pom</type>
            <version>${cus-lib.version}</version>
        </dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-persistence-mybatis-support</artifactId>
			<version>${cus-lib.version}</version>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-rest-consumer-support</artifactId>
			<version>${cus-lib.version}</version>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-storage-support</artifactId>
			<version>${cus-lib.version}</version>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-log-support</artifactId>
			<version>${cus-lib.version}</version>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-rest-provider-support</artifactId>
			<version>${cus-lib.version}</version>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-message-queue-support</artifactId>
			<version>${cus-lib.version}</version>
		</dependency>

		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-scheduler-support</artifactId>
			<version>${cus-lib.version}</version>
		</dependency>



		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.12.0</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.alibaba.fastjson2</groupId>
			<artifactId>fastjson2</artifactId>
			<version>2.0.51</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.9.19</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
<!--			<scope>runtime</scope>-->
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>8.4.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/io.micrometer/micrometer-registry-prometheus -->
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
			<version>1.13.1</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.sonarsource.scanner.maven</groupId>
				<artifactId>sonar-maven-plugin</artifactId>
				<version>${sonar-plugin.version}</version>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>${jacoco-plugin.version}</version>
				<executions>
					<execution>
						<id>prepare-jacoco</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<finalName>cus-qa</finalName>
<!--					<fork>true</fork>-->
					<mainClass>com.kering.cus.qa.QaApplication</mainClass>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
<!--			<plugin>-->
<!--				<groupId>com.github.spotbugs</groupId>-->
<!--				<artifactId>spotbugs-maven-plugin</artifactId>-->
<!--				<version>${spotbugs-plugin.version}</version>-->
<!--				<configuration>-->
<!--					<excludeFilterFile>spotbugs-security-exclude.xml</excludeFilterFile>-->
<!--					<plugins>-->
<!--						<plugin>-->
<!--							<groupId>com.h3xstream.findsecbugs</groupId>-->
<!--							<artifactId>findsecbugs-plugin</artifactId>-->
<!--							<version>${findsecbugs-plugin.version}</version>-->
<!--						</plugin>-->
<!--					</plugins>-->
<!--				</configuration>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<id>check</id>-->
<!--						<phase>test</phase>-->
<!--						<goals>-->
<!--							<goal>check</goal>-->
<!--						</goals>-->
<!--					</execution>-->
<!--				</executions>-->
<!--			</plugin>-->
		</plugins>
	</build>
	<!--<repositories>
		<repository>
			<id>arsenal-jfrog-snapshot</id>
			<name>arsenal-jfrog-virtual</name>
			<url>${private-maven.snapshot}</url>
		</repository>
		<repository>
			<id>arsenal-jfrog-release</id>
			<name>arsenal-jfrog-virtual</name>
			<url>${private-maven.release}</url>
		</repository>
	</repositories>

	<distributionManagement>
		<snapshotRepository>
			<id>arsenal-jfrog-snapshot</id>
			<url>${private-maven.snapshot}</url>
		</snapshotRepository>
		<repository>
			<id>arsenal-jfrog-release</id>
			<url>${private-maven.release}</url>
		</repository>
	</distributionManagement>-->
</project>
