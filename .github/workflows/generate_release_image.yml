# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-java-with-maven

name: (Auto) Generate Release Image

on:
  push:
    tags:
      - v*

env:
  ARTIFACTORY_DOCKER_REGISTRY: ${{ vars.ARTIFACTORY_DOCKER_REGISTRY }}
  ARTIFACTORY_DOCKER_REPO: ${{ vars.ARTIFACTORY_DOCKER_REPO }}
  ARTIFACTORY_URL: ${{ vars.ARTIFACTORY_URL }}
  HELM_REPO: ${{ vars.HELM_REPO }}
  TAG_NAME: ${{ github.ref_name }}
  VAULT_ADDRESS_PROD: ${{ vars.VAULT_ADDRESS_PROD }}
  VAULT_GITHUB_ROLE: ${{ vars.VAULT_GITHUB_ROLE }}
  VAULT_GITHUB_PATH: ${{ vars.VAULT_GITHUB_PATH }}
  vars: ${{ toJSON(vars) }}
  
jobs:
  build_jar:
    permissions:
      contents: read
      id-token: write
    runs-on:
      - kt-gu-cn
    outputs:
      jar_maven_repo_url: ${{ steps.mvn_deploy.outputs.url }}
    steps:
    - uses: kering-technologies-china/dso-cus-github-action/microservice/setup@main
      name: Setup Environment
      with:
        java-version: 21
        maven-version: 3.9.6
    - name: Deploy jar to jfrog
      uses: kering-technologies-china/dso-cus-github-action/microservice/maven-deploy@main
      id: mvn_deploy
      with:
        repo-name: ${{ vars.MAVEN_RELEASE_REPO }}
        tag: ${{ github.ref_name }}
  build_image:
    permissions:
      contents: read
      id-token: write
    runs-on:
      - ${{ vars.IMAGE_BUILD_RUNNER }}
    needs: [build_jar]
    outputs:
      tag: ${{ steps.kaniko_build.outputs.tag }}
    container:
      image: "${{ vars.KANIKO_IMAGE }}"
      credentials:
        username: ${{ secrets.ARTIFACTORY_USERNAME }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}
    steps:
    - name: Build image
      uses: kering-technologies-china/dso-cus-github-action/microservice/kaniko-build@main
      id: kaniko_build
      with:
        tag: ${{ env.TAG_NAME }}
        jar-maven-repo-url: ${{ needs.build_jar.outputs.jar_maven_repo_url }}
    - name: Jfrog scan image
      uses: kering-technologies-china/dso-cus-github-action/jfrog-scan@main
      with:
        target: "${{ env.projectName }}.${{ env.tag }}.tar"