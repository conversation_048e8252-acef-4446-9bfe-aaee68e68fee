# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-java-with-maven

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: (Auto) Deploy To Dev

on:
  push:
    branches: [ "main" ]
    
env:
  environment: dev
  cluster: cus-nonprod
  ARTIFACTORY_DOCKER_REGISTRY: ${{ vars.ARTIFACTORY_DOCKER_REGISTRY }}
  ARTIFACTORY_DOCKER_REPO: ${{ vars.ARTIFACTORY_DOCKER_REPO }}
  ARTIFACTORY_URL: ${{ vars.ARTIFACTORY_URL }}
  HELM_REPO: ${{ vars.HELM_REPO }}
  VAULT_ADDRESS_PROD: ${{ vars.VAULT_ADDRESS_PROD }}
  VAULT_GITHUB_ROLE: ${{ vars.VAULT_GITHUB_ROLE }}
  VAULT_GITHUB_PATH: ${{ vars.VAULT_GITHUB_PATH }}
  vars: ${{ toJSON(vars) }}
  
jobs:
  build_jar:
    permissions:
      contents: read
      id-token: write
    runs-on:
      - kt-gu-cn
    outputs:
      jar_maven_repo_url: ${{ steps.mvn_deploy.outputs.url }}
    steps:
    - uses: kering-technologies-china/dso-cus-github-action/microservice/setup@feature/PKCUS-6401
      name: Setup Environment
      with:
        java-version: 21
        maven-version: 3.9.6
    - name: Deploy jar to jfrog
      uses: kering-technologies-china/dso-cus-github-action/microservice/maven-deploy@feature/PKCUS-6401
      id: mvn_deploy
      with:
        repo-name: ${{ vars.MAVEN_SNAPSHOT_REPO }}
        arsenal-jfrog-id: arsenal-jfrog-snapshot
  build_image:
    permissions:
      contents: read
      id-token: write
    runs-on:
      - ${{ vars.IMAGE_BUILD_RUNNER }}
    needs: [build_jar]
    outputs:
      tag: ${{ steps.kaniko_build.outputs.tag }}
    container:
      image: "${{ vars.KANIKO_IMAGE }}"
      credentials:
        username: ${{ secrets.ARTIFACTORY_USERNAME }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}
    steps:
    - name: Build image
      uses: kering-technologies-china/dso-cus-github-action/microservice/kaniko-build@feature/PKCUS-6401
      id: kaniko_build
      with:
        jar-maven-repo-url: ${{ needs.build_jar.outputs.jar_maven_repo_url }}
    - name: Jfrog scan image
      uses: kering-technologies-china/dso-cus-github-action/jfrog-scan@main
      with:
        target: "${{ env.projectName }}.${{ env.tag }}.tar"
  helm_update:
    permissions:
      contents: read
      id-token: write
    runs-on:
    - kt-gu-cn
    needs: [build_jar, build_image]
    steps:
    - name: Update tag
      uses: kering-technologies-china/dso-cus-github-action/microservice/helm-update@main
      with:
        tag: ${{ needs.build_image.outputs.tag }}
        app-fullname: ${{ github.repository }}
        cluster: ${{ env.cluster }}
        env: ${{ env.environment }}
    - name: Post deploy
      uses: kering-technologies-china/dso-cus-github-action/post-deploy@main