ARG JFROG_URL
ARG JFROG_DOCKER_LOCAL_REPO
FROM ${JFROG_URL}/${JFROG_DOCKER_LOCAL_REPO}/jf-cli:v2 as jfrog
ARG JFROG_URL
ARG JFROG_USERNAME
ARG JFROG_PASSWORD
ARG JAR_MAVEN_REPO_URL
ARG JFROG_DOCKER_LOCAL_REPO
ARG BASE_IMAGE
ARG APM_AGENT_URL
ARG JFROG_SERVER_ID
WORKDIR /build
RUN jf c add ${JFROG_SERVER_ID} --basic-auth-only --url  "https://${JFROG_URL}" --user ${JFROG_USERNAME} --password ${JFROG_PASSWORD}
# Download java agent from jfrog
RUN jf rt dl --flat ${APM_AGENT_URL}  opentelemetry-javaagent.jar

# Download app jar form jfrog
RUN jf rt dl --flat --sort-by=created --sort-order=desc --limit=1 "${JAR_MAVEN_REPO_URL}/*.jar" app.jar

FROM ${JFROG_URL}/${JFROG_DOCKER_LOCAL_REPO}/${BASE_IMAGE}

EXPOSE 8080

# RUN echo "Asia/Shanghai" > /etc/timezone
RUN adduser cus-ms-user -D
WORKDIR /home/<USER>
COPY --from=jfrog ./build/*.jar .
RUN chown cus-ms-user:cus-ms-user *
USER cus-ms-user

# JVM PARAMETERS
ENV JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError

# INJECT CONFIG FILES AND SECRETS
ENV CONFIG_FILES_PATH "/configs/files/"
ENV CONFIG_SECRET_PATH "/configs/secret/"

# PROFILE ENVIRONMENT
ENV ENV "injected"

# AliCloud Database Configuration
ENV DATABASE_URL "injected"
ENV DATABASE_USERNAME "injected"
ENV DATABASE_PASSWORD "injected"

# AliCloud KMS Configuration
ENV KMS_REGION_ID "injected"
ENV KMS_ENDPOINT "injected"
ENV KMS_SECRET_PATH "injected"

# AliCloud Scheduler X Configuration
ENV SCHEDULE_X2_ENDPOINT "injected"
ENV SCHEDULE_X2_NAMESPACE "injected"
ENV SCHEDULE_X2_GROUPID "injected"
ENV SCHEDULE_X2_APPKEY "injected"

# Dependended service store location endpoint
ENV CUS_STORE_LOCATOR_REST_ENDPOINT "injected"

# AliCloud Kafka Producer Configuration
ENV TOPIC_NAME "injected"
ENV TOPIC_GROUP "injected"
ENV TOPIC_BROKER "injected"

# Open Telemetry endpoint and token
ENV OTEL_ENDPOINT "injected"
ENV OTEL_TOKEN "injected"

# File Storage For OSS
ENV BUCKET_ROOT_PATH "injected"
ENV STORAGE_OSS_SECRET_ACCESS_PATH "injected"
ENV STORAGE_OSS_ACCESS_KEY_ID "injected"
ENV STORAGE_OSS_ACCESS_KEY_SECET "injected"

#ENV KMS_CLIENT_KEY_PASS "injected"
#ENV KMS_CLIENT_KEY_CONTENT "injected"
#ENV KMS_CA_CERT "injected"


CMD ["sh", "-c", "java ${JVM_OPTS} \
    -Duser.timezone=\"Asia/Shanghai\" \
    -Duser.language=\"zh\" \
    -Dspring.profiles.active=${ENV} \
    -Dconfig.filePath=${CONFIG_FILES_PATH} \
    -Dconfig.secretPath=${CONFIG_SECRET_PATH} \
    -Dpersistence.database.url=${DATABASE_URL} \
    -Dkms.endpoint=${KMS_ENDPOINT} \
    -Dsecret.path=${KMS_SECRET_PATH} \
    -Dspring.schedulerx2.endpoint=${SCHEDULE_X2_ENDPOINT} \
    -Dspring.schedulerx2.namespace=${SCHEDULE_X2_NAMESPACE} \
    -Dspring.schedulerx2.groupId=${SCHEDULE_X2_GROUPID} \
    -Dspring.schedulerx2.appKey=${SCHEDULE_X2_APPKEY} \
    -Dcus.store-locator.rest.endpoint=${CUS_STORE_LOCATOR_REST_ENDPOINT} \
    -Dtopic.name=${TOPIC_NAME} \
    -Dtopic.group=${TOPIC_GROUP} \
    -Dtopic.broker=${TOPIC_BROKER} \
    -jar app.jar"]

#    Below is the old version that inject the credentials and certificates via the property, but we suggest reading those
#    values from the config.secretPath
#    -Dpersistence.database.username=${DATABASE_USERNAME} \
#    -Dpersistence.database.password=${DATABASE_PASSWORD} \
#    -Dkms.clientKeyPass=${KMS_CLIENT_KEY_PASS} \
#    -Dkms.clientKeyContent=\"${KMS_CLIENT_KEY_CONTENT}\" \
#    -Dkms.caCert=${KMS_CA_CERT} \