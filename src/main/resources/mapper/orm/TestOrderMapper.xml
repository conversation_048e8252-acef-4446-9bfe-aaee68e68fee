<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kering.cus.qa.mapper.orm.TestOrderMapper">
    <delete id="deleteId">
        DELETE FROM test_order WHERE id = #{id};
    </delete>

    <delete id="deleteAll">
        DELETE FROM test_order;
    </delete>

    <update id="updateByCondition">
        UPDATE test_order
        SET price = #{entity.price}
        <where>
            id = #{entity.id}
            <if test="sqlConditionList != null and !sqlConditionList.isEmpty()">
                <foreach collection="sqlConditionList" item="condition" separator=" AND ">
                    <choose>
                        <when test="condition.conditionType.toString() == 'EQUALS'">
                            ${condition.column} = #{condition.value}
                        </when>
                        <when test="condition.conditionType.toString() == 'LIKE'">
                            ${condition.column} LIKE CONCAT('%', #{condition.value}, '%')
                        </when>
                        <when test="condition.conditionType.toString() == 'GREATER_THAN'">
                            ${condition.column} &gt; #{condition.value}
                        </when>
                        <when test="condition.conditionType.toString() == 'LESS_THAN'">
                            ${condition.column} &lt; #{condition.value}
                        </when>
                        <when test="condition.conditionType.toString() == 'GREATER_THAN_OR_EQUAL'">
                            ${condition.column} &gt;= #{condition.value}
                        </when>
                        <when test="condition.conditionType.toString() == 'LESS_THAN_OR_EQUAL'">
                            ${condition.column} &lt;= #{condition.value}
                        </when>
                        <otherwise>
                            ${condition.column} = #{condition.value}
                        </otherwise>
                    </choose>
                </foreach>
            </if>
        </where>
    </update>

</mapper>