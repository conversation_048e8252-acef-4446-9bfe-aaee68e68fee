<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kering.cus.qa.mapper.lock.TestOrderLockMapper">
    <update id="updatePriceByWhere">
        UPDATE test_order
        SET price = #{entity.price}
        WHERE id = #{entity.id}
          AND price = #{price}
    </update>
    <update id="updatePriceByVersionEqual">
        UPDATE test_order
        SET price = #{entity.price}
        WHERE id = #{entity.id}
            and version = #{version}
    </update>
    <update id="updatePriceByVersionGreaterThan">
        UPDATE test_order
        SET price = #{entity.price}
        WHERE id = #{entity.id}
        and version &gt; #{version}
    </update>
    <update id="updatePriceByVersionLessThan">
        UPDATE test_order
        SET price = #{entity.price}
        WHERE id = #{entity.id}
        and  version &lt; #{version}
    </update>


    <delete id="updatePriceByWhere">
        delete from test_order
        WHERE id = #{entity.id}
        AND price = #{price}
    </delete>
    <delete id="updatePriceByVersionEqual">
        delete from test_order
        WHERE id = #{entity.id}
        and version = #{version}
    </delete>
    <delete id="updatePriceByVersionGreaterThan">
        delete from test_order
        WHERE id = #{entity.id}
        and version &gt; #{version}
    </delete>
    <delete id="updatePriceByVersionLessThan">
        delete from test_order
        WHERE id = #{entity.id}
        and  version &lt; #{version}
    </delete>
</mapper>