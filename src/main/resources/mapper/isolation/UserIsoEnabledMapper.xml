<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kering.cus.qa.mapper.isolation.UserIsoEnabledMapper">
    <insert id="insertUserIsoEnabled" parameterType="com.kering.cus.qa.entity.isolation.UserIsoEnabledEntity">
        INSERT INTO user_iso_enabled(id,name,age,tenant_id,created_date,modified_date)
        VALUES (#{id}, #{name}, #{age}, #{tenantId}, #{createdDate}, #{modifiedDate})
    </insert>
    <update id="updateUserIsoEnabledById">
        UPDATE
        user_iso_enabled
        SET
        name = #{name},
        age = #{age}
        WHERE id=#{id};
    </update>
    <update id="updateUserIsoEnabledByName">
        UPDATE
        user_iso_enabled
        SET
        age = #{age}
        WHERE name = #{name};
    </update>

    <delete id="deleteUserIsoEnabledById">
        DELETE FROM user_iso_enabled where id = #{id};
    </delete>
    <select id="selectUserIsoEnabledById" resultType="com.kering.cus.qa.entity.isolation.UserIsoEnabledEntity">
        select * from user_iso_enabled where id = #{id};
    </select>
    <select id="selectUserIsoEnabledByTenantId" resultType="com.kering.cus.qa.entity.isolation.UserIsoEnabledEntity">
        select * from user_iso_enabled where tenant_id = #{tenantId};
    </select>
    <select id="selectUserIsoEnabledByName" resultType="com.kering.cus.qa.entity.isolation.UserIsoEnabledEntity">
        select * from user_iso_enabled where name = #{name};
    </select>


</mapper>