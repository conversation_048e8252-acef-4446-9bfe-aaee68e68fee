<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kering.cus.qa.mapper.isolation.UserIsoDisabledMapper">
    <insert id="insertUserIsoDisabled" parameterType="com.kering.cus.qa.entity.isolation.UserIsoDisabledEntity">
        INSERT INTO user_iso_disabled(id,name,age,tenant_id,created_date,modified_date)
        VALUES (#{id}, #{name}, #{age}, #{tenantId}, #{createdDate}, #{modifiedDate})
    </insert>
    <delete id="deleteAll">
        DELETE FROM user_iso_disabled;
    </delete>
    <select id="selectUserIsoDisabledById" resultType="com.kering.cus.qa.entity.isolation.UserIsoDisabledEntity">
        select * from user_iso_disabled where id = #{id};
    </select>
</mapper>