<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kering.cus.qa.mapper.isolation.UserForModifyIsoEnabledMapper">
    <delete id="deleteAll">
        DELETE FROM user_for_modify_iso_enabled;
    </delete>

    <update id="updateUserForModifyIsoEnabledByTenantId">
        UPDATE
        user_for_modify_iso_enabled
        SET
        name = #{name},
        age = #{age}
        WHERE tenant_id=#{tenantId};
    </update>


    <delete id="deleteUserForModifyIsoEnabledByTenantId">
        DELETE FROM user_for_modify_iso_enabled where tenant_id=#{tenantId};
    </delete>
    <delete id="deleteUserForModifyIsoEnabledByName">
        DELETE FROM user_for_modify_iso_enabled where name=#{name};
    </delete>
</mapper>