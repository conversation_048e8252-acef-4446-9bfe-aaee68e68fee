<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kering.cus.qa.mapper.isolation.UserAccountIsoEnabledMapper">
    <delete id="deleteAll">
        DELETE FROM user_iso_enabled;
    </delete>
    <select id="selectUserAccountEntityByUserId"
            resultType="com.kering.cus.qa.entity.isolation.UserAccountIsoEnabledEntity">
        SELECT ui.id user_id,ui.name user_name,ui.tenant_id user_tenant_id,uai.id acc_id,uai.account_name
        acc_name,uai.tenant_id account_tenant_id FROM user_iso_enabled ui LEFT JOIN user_account_iso_enabled uai
        ON ui.id=uai.user_id WHERE ui.id=#{id};
    </select>
    <select id="selectUserAccountEntityByUserId2"
            resultType="com.kering.cus.qa.entity.isolation.UserAccountIsoEnabledEntity">
        SELECT ui.id user_id,ui.name user_name,ui.tenant_id user_tenant_id,uai.id acc_id,uai.account_name
        acc_name,uai.tenant_id account_tenant_id FROM user_iso_enabled ui, user_account_iso_enabled uai WHERE
        ui.id=uai.user_id AND ui.id=#{id};
    </select>
</mapper>