spring.devtools.restart.enabled=true
spring.application.name= cus-qa
server.port= 9100
logging.config=classpath:logback.xml
mybatis-plus.mapper-locations=classpath:mapper/**/*.xml

persistence.database.tenant.isolation.enabled=false

#secret.provider=property
#secret.provider=file
secret.provider = kms
#secret.provider = vault
secret.ramuser.enabled = true

storage.provider=oss
storage.oss.endpoint=https://oss-cn-hangzhou.aliyuncs.com
storage.oss.region=cn-hangzhou

#storage.provider=file
#storage.file.root=D:\\integration-test-cus-lib

#com.kering.AccessKeyId=LTAI5tMnKuaUN9YGZ1J3NyTf
#com.kering.AccessKeySecret=******************************
#scheduler.provider=schedulerx

#kafka-Local-properties
#kafka.bootstrap-servers=10.88.108.65:9092
#kafka.consumer.group-id=CG_KERING_QUEUE_TEST
#kafka.ssl.enabled=false
#app.name=KERING_TEST
#kafka.consumer.dlt-topic=MQ_KERING_QUEUE_TEST
kafka.consumer.dlt-strategy-enabled=true
kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer
kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.JsonDeserializer
#kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
#kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.main.allow-bean-definition-overriding=true
queue.provider=kafka
#kafka-Dev-properties
#kafka.consumer.group-id=CG_Partner_CUS_PLATFORM_QA
kafka.ssl.key-store-type=jks
kafka.security.protocol=ssl
app.name=QA_TEST


#kafka-Dev-properties
#kafka.bootstrap-servers=kafka1.qa.nrt.keringcn-syss.com:19093,kafka2.qa.nrt.keringcn-syss.com:19093,kafka3.qa.nrt.keringcn-syss.com:19093
##kafka.bootstrap-servers=10.88.108.65:9092
#kafka.consumer.group-id=CG_Partner_CUS_PLATFORM_QA
#kafka.ssl.enabled=true
#app.name=KERING_TEST
#kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer
#kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.JsonDeserializer
#kafka.ssl.trust-store.password=PASSWORD_BUNDLE123
#kafka.ssl.key-store.password=PASSWORD_BUNDLE123
#kafka.ssl.key.password=PASSWORD_BUNDLE123
#kafka.consumer.client-id-prefix=cus-service
#kafka.ssl.trust-store.location=D:/kafka/truststore.jks
#kafka.ssl.key-store.location=D:/kafka/keystore.jks
#kafka.ssl.trust-store.cert.path=D:/kafka/cert/truststore.txt
#kafka.ssl.key-store.cert.path=D:/kafka/cert/keystore.txt
#queue.provider=kafka


management.endpoints.web.exposure.include=health,prometheus
management.endpoint.health.probes.enabled=false
management.endpoint.health.show-details=always
management.endpoint.prometheus.enabled=true
management.metrics.export.prometheus.enabled=true
kafka.producer.metrics-enabled=false
kafka.producer.log-enabled=false
kafka.producer.encrypt-enabled=false
kafka.consumer.log-enabled=false
kafka.consumer.metrics-enabled=false



rest-consumer.exchange.config.default.max-in-memory-size=512
rest-consumer.exchange.config.default.logger-level=NONE
rest-consumer.exchange.config.default.http-connect-timeout=5000
rest-consumer.exchange.config.default.http-response-timeout=6000
rest-consumer.exchange.config.customCommunicationClient.http-response-timeout=8000

normaluser.name=testname
normaluser.password=testpassword
normaluser.port=1000

userkeylack.name=testname
userkeylack.password=testpassword

userkeylong.name=testname
userkeylong.password=testpassword
userkeylong.port=1000
userkeylong.url=testUrl

userPropertyNull.name=testname
userPropertyNull.password=testpassword
userPropertyNull.port=

userSerializableError.name=testname
userSerializableError.password=testpassword
userSerializableError.port=1000a


#ALICLOUD_OTEL_URL=http://tracing-analysis-dc-hz-internal.aliyuncs.com
#DATABASE_URL=************************************************************************************************************
#KMS_REGION_ID= cn-hangzhou
#KMS_ENDPOINT=kst-hzz6656d07d9uujh3n4ss.cryptoservice.kms.aliyuncs.com
#KMS_CLIENT_KEY_FILE_PATH=/configs/secret/KMS_CLIENT_KEY_CONTENT
#KMS_CLIENT_KEY_PASSWORD_FILE_PATH= /configs/secret/KMS_CLIENT_KEY_PASS
#KMS_IGNORE_SSL_CERTS= "false"
#KMS_CA_FILE_PATH= /configs/secret/KMS_CA_CERT
#BUCKET_ROOT_PATH= /integration-test-cus-lib/sit
#STORAGE_OSS_SECRET_ACCESS_PATH= acs/ram/user/cus-core-private-nonprod
#KMS_RAM_USER_SECRET_NAME= acs/ram/user/cus-core-private-nonprod
#database.client.sslKey=dGVzdA==