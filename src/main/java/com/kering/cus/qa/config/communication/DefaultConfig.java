//package com.kering.cus.qa.config.communication;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.http.HttpStatusCode;
//import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
//import org.springframework.web.reactive.function.client.ExchangeFilterFunctions;
//
//
//@Slf4j
//@Configuration
//public class DefaultConfig {
//
//
//    @Bean
//    public ExchangeFilterFunction customStatusHandleFilter() {
//        return ExchangeFilterFunctions.statusError(HttpStatusCode::is2xxSuccessful, resp -> {
//            log.error("Request Info. Url: {}, StatusCode: {}", resp.request().getURI(), resp.statusCode());
//            return resp.createException().block();
//        });
//    }
//}
//
