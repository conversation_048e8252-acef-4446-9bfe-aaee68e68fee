package com.kering.cus.qa.config;


import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.kms.model.v20160120.GetSecretValueRequest;
import com.aliyuncs.kms.model.v20160120.GetSecretValueResponse;
import com.aliyuncs.kms.secretsmanager.client.SecretCacheClient;
import com.aliyuncs.kms.secretsmanager.client.SecretCacheClientBuilder;
import com.aliyuncs.kms.secretsmanager.client.exception.CacheSecretException;
import com.aliyuncs.kms.secretsmanager.client.service.SecretManagerClient;
import com.aliyuncs.kms.secretsmanager.client.utils.CacheClientConstant;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kering.cus.lib.secret.access.implementation.KMSSecretAccessService;
import com.kering.cus.lib.secret.access.implementation.LocalFileSecretAccessService;
import com.kering.cus.lib.secret.access.implementation.PropertySecretAccessService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertiesPropertySource;

import java.io.IOException;
import java.util.Properties;


/***
 * Alicloud KMS Configuration <a href="https://www.alibabacloud.com/help/en/kms/support/secrets-manager-client-1">Dedicated kms sdk for java</a>
 */
@Configuration
@Slf4j
public class InitBeanConfig {

    @Autowired
    private ConfigurableEnvironment environment;


//    @Bean("localFileSecretAccessService")
//    public LocalFileSecretAccessService LocalFileSecretAccessService() {
//        return new LocalFileSecretAccessService();
//    }


//    @Bean("mutablePropertySources")
//    public MutablePropertySources initMutablePropertySources() {
//        Properties properties = new Properties();
//        properties.put("tactiq.data.cus.cuspoc.cuspoc-nonprod-alicloud.key", "value");
//        PropertiesPropertySource propertiesPropertySource = new PropertiesPropertySource("DefaultProperties", properties);
//
//        MutablePropertySources sources = new MutablePropertySources();
//        sources.addFirst(propertiesPropertySource);
//        return sources;
//    }

//    @Bean("propertySecretAccessService")
//    public PropertySecretAccessService initPropertySecretAccessService() {
//        return new PropertySecretAccessService(environment);
//    }

//    @Bean("kMSSecretAccessService")
//    public KMSSecretAccessService KMSSecretAccessService() throws CacheSecretException {
//        MockSecretManagerClient mockSecretManagerClient = new MockSecretManagerClient();
//        return new KMSSecretAccessService(SecretCacheClientBuilder.newCacheClientBuilder(mockSecretManagerClient).build());
//    }


    public static class MockSecretManagerClient implements SecretManagerClient {

        public static String key;

        @Override
        public void init() throws CacheSecretException {

        }

        @Override
        public GetSecretValueResponse getSecretValue(GetSecretValueRequest req) throws ClientException {
            GetSecretValueResponse response = new GetSecretValueResponse();
            response.setSecretName(req.getSecretName());
            response.setSecretData(key);
            response.setVersionId("v1");
            response.setSecretDataType(CacheClientConstant.TEXT_DATA_TYPE);
            return response;
        }

        @Override
        public void close() throws IOException {

        }
    }
}