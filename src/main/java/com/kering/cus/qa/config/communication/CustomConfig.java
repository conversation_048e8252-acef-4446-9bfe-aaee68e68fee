//package com.kering.cus.qa.config.communication;
//
//import com.kering.cus.qa.filter.CommunicationCustomLogFilter;
//import org.springframework.context.annotation.Bean;
//import org.springframework.web.reactive.function.client.WebClient;
//import org.springframework.web.reactive.function.client.support.WebClientAdapter;
//
//public class CustomConfig {
//
//
//    @Bean
//    public WebClientAdapter customWebClient(CommunicationCustomLogFilter logFilter) {
//
//        WebClient webClient = WebClient.builder()
//                .defaultRequest(requestHeadersSpec -> requestHeadersSpec.attribute("sign", "test"))
//                .filter(logFilter)  // 集成Service Communication SDK中的统一日志Filter（可选的）
//                .build();
//        return WebClientAdapter.create(webClient);
//    }
//
//    @Bean
//    public CommunicationCustomLogFilter createCustomUnifiedLogFilter() {
//        return new CommunicationCustomLogFilter();
//    }
//
//}
//
