//package com.kering.cus.qa.config.queue;
//
//import com.alibaba.fastjson2.JSON;
//import com.kering.cus.lib.common.context.GenericRequestContextHolder;
//import com.kering.cus.lib.message.queue.consumer.MessageConsumer;
//import com.kering.cus.lib.message.queue.consumer.annotation.MessageListener;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.stereotype.Component;
//
//import static com.kering.cus.qa.constants.KafkaSdkConstants.*;
//
//@Slf4j
//@Component
//public class DoubleListener {
//    /**
//     * tc2_1_2_2，tc2_1_3_2，(no consumer : tc2_1_1_2，tc2_1_3_4)，(encrypt : tc2_2_1_1，tc2_2_1_2) use
//     */
//    @MessageListener(topic = "${kafka.consumer.dlt-topic}", groupId = GROUP_ID, eventType = EVENT_TYPE)
//    /**
//     * tc2_1_1_1，tc2_1_3_1，(no consumer : tc2_1_3_3) use
//     */
//    //@MessageListener(topic = TOPIC_ONE, groupId = GROUP_ID)
//    /**
//     * tc2_1_2_1 use，no consumer
//     */
//    //@MessageListener(topic = TOPIC_ONE, eventType = EVENT_TYPE)
//    /**
//     * tc2_1_2_3 use，no consumer
//     */
//    //@MessageListener(topic = TOPIC_TWO, groupId = GROUP_ID, eventType = EVENT_TYPE)
//    /**
//     * tc2_1_2_4 use，no consumer
//     */
//    //@MessageListener(topic = TOPIC_TWO, groupId = "testGroup", eventType = EVENT_TYPE)
//    /**
//     * tc2_1_2_5 use，no consumer
//     */
//    //@MessageListener(topic = TOPIC_TWO, groupId = GROUP_ID, eventType = "noExistEventType")
//    /**
//     * tc2_1_2_6 use，no consumer
//     */
//    //@MessageListener(topic = TOPIC_TWO, groupId = "testGroup", eventType = "noExistEventType")
//    /**
//     * tc2_1_2_7 use，no consumer
//     */
//    //@MessageListener(topic = TOPIC_ONE, groupId = "testGroup", eventType = EVENT_TYPE)
//    /**
//     * tc2_1_2_8 use，no consumer
//     */
//    //@MessageListener(topic = TOPIC_ONE, groupId = "testGroup", eventType = "noExistEventType")
//    /**
//     * tc2_1_2_9 use，no consumer
//     */
//    //@MessageListener(topic = TOPIC_ONE, groupId = GROUP_ID, eventType = "noExistEventType")
//    @Component
//    public class AllVarListener implements MessageConsumer<Double> {
//
//        @Override
//        public void consume(Double message) {
//            log.info("AllVarListener Double consumer message: " + JSON.toJSONString(message));
//        }
//    }
//
//    //------------------------------------------------------------------------------------------------------------------------------------------------------------------------
//
//    /**
//     * tc2_1_4 use
//     */
//    @MessageListener(topic = TOPIC_ONE, groupId = GROUP_ID, eventType = EVENT_TYPE_TEST)
//    @Component
//    public class RequestPropertiesListener implements MessageConsumer<Double> {
//        @Override
//        public void consume(Double message) {
//            GenericRequestContextHolder.getRequestProperty().ifPresent(requestProperty -> log.error("RequestPropertiesListener Double consumer properties:{}", JSON.toJSONString(requestProperty.getRequestAttributeMaps())));
//        }
//    }
//
//    //------------------------------------------------------------------------------------------------------------------------------------------------------------------------
//
//    /**
//     * tc3_1_1_1,tc3_1_1_2 use
//     */
//    @MessageListener(topic = TOPIC_ONE, groupId = GROUP_ID, eventType = EVENT_TYPE_ERROR)
//    @Component
//    public class ErrorListener implements MessageConsumer<Double> {
//        @Override
//        public void consume(Double message) {
//            log.info("ErrorListener Double consumer message: " + JSON.toJSONString(message));
//            throw new RuntimeException("error");
//        }
//    }
//
//
//    //------------------------------------------------------------------------------------------------------------------------------------------------------------------------
//
//    /**
//     * tc2_2_1_3 use
//     */
//    @MessageListener(topic = TOPIC_ONE, groupId = GROUP_ID, eventType = EVENT_TYPE_ENCRYPT)
//    @Component
//    public class EncryptListener implements MessageConsumer<Double> {
//
//        @Override
//        public void consume(Double message) {
//            log.info("EncryptListener Double consumer message: " + JSON.toJSONString(message));
//        }
//    }
//
//
//    //------------------------------------------------------------------------------------------------------------------------------------------------------------------------
//
//    /**
//     * tc3_1_1_1,tc3_1_1_2 use
//     */
//    @MessageListener(topic = TOPIC_DLT, groupId = GROUP_ID, eventType = EVENT_TYPE_ERROR)
//    @Component
//    public class DltListener implements MessageConsumer<Double> {
//
//        @Override
//        public void consume(Double message) {
//            log.info("DltListener Double consumer message: " + JSON.toJSONString(message));
//        }
//    }
//
//    /**
//     * tc3_1_1_3,tc3_1_1_4 use
//     */
//
//    @Component
//    public class NativeListener {
//
//        @KafkaListener(topics = TOPIC_ONE+"_DOUBLE", groupId = GROUP_ID)
//        public void consume(Double message) {
//            log.info("NativeListener Double consumer message: " + JSON.toJSONString(message));
//        }
//    }
//}