package com.kering.cus.qa;

import com.kering.cus.lib.rest.consumer.exchange.autoinject.HttpExchangesScan;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@SpringBootApplication
@EnableAspectJAutoProxy(proxyTargetClass = true)
@ComponentScan(basePackages = {"com.kering.cus.qa","com.kering.cus.lib.**"})
@MapperScan("com.kering.cus.qa.mapper")
@HttpExchangesScan(basePackages = {"com.kering.cus.qa.feign"})
@EnableFeignClients
@Slf4j
public class QaApplication {

	public static void main(String[] args) {
		SpringApplication.run(QaApplication.class,args);
	}

}
