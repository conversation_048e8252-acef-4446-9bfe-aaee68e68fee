package com.kering.cus.qa.constants;


import com.kering.cus.qa.entity.orm.UserConfig;
import com.kering.cus.qa.entity.orm.UserTitleConfig;

import java.util.ArrayList;
import java.util.List;

public class SecretManagementSdkConstants {

    public static final UserConfig USER = new UserConfig("testname", "testpassword", 1000);
    public static final UserTitleConfig USER_TITLE = new UserTitleConfig("testname", "testpassword", 1000, "testdbName");


    public static final List<String> FILE_PATH_LIST = new ArrayList<>() {{
        //00
        add("/home/<USER>/cus/test/testapplication/sit/user");
        //01
        add("/home/<USER>/cus/test/testapplication/sit/userkeylack");
        //02
        add("/home/<USER>/cus/test/testapplication/sit/userkeylong");
        //03
        add("/home/<USER>/cus/test/testapplication/sit/userLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLo");
        //04
        add("/home/<USER>/cus/test/testapplication/sit/userserializableerror");
        //05
        add("/home/<USER>/cus/test/testapplication/sit/usernon");
        //06
        add("/home/<USER>/cus/test/testapplication/sit/usertitlecasenormal");
        //07
        add("/home/<USER>/cus/test/testapplication/sit/usertitlecaseupper");
        //08
        add("/home/<USER>/cus/test/testapplication/sit/usertitlecase");
        //09
        add("/home/<USER>/home/<USER>/test/testapplication/sit/userLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongL");
        //10
        add("/home/<USER>/cus/test/testapplication/sit/userPropertyNull");
        //11
        add("/home/<USER>/cus/test/testapplication/sit/userupdate");
        //12
        add("/home/<USER>/cus/test/testapplication/sit/userdel");
    }};


    public static final List<String> KMS_PATH_LIST = new ArrayList<>() {{
        //00
        add("/cus/test/testapplication/sit/user");
        //01
        add("/cus/test/testapplication/sit/userkeylack");
        //02
        add("/cus/test/testapplication/sit/userkeylong");
        //03
        add("/cus/test/testapplication/sit/userLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLo");
        //04
        add("/cus/test/testapplication/sit/userserializableerror");
        //05
        add("/cus/test/testapplication/sit/usernon");
        //06
        add("/cus/test/testapplication/sit/usertitlecasenormal");
        //07
        add("/cus/test/testapplication/sit/usertitlecaseupper");
        //08
        add("/cus/test/testapplication/sit/usertitlecase");
        //09
        add("/home/<USER>/test/testapplication/sit/userLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongL");
        //10
        add("/cus/test/testapplication/sit/userPropertyNull");
        //11
        add("/cus/test/testapplication/sit/userupdate");
        //12
        add("/cus/test/testapplication/sit/userdel");
    }};

    public static final List<String> KEY_LIST = new ArrayList<>() {{
        //00
        add("dbName");
        //01
        add("name");
        //02
        add("nullName");
        //03
        add("port");
        //04
        add("password");
    }};

    public static final List<String> PROPERTY_PATH_LIST = new ArrayList<>() {{
        //00
        add("normaluser");
        //01
        add("userkeylack");
        //02
        add("userkeylong");
        //03
        add("userLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLongLong");
        //04
        add("userPropertyNull");
        //05
        add("userSerializableError");
        //06
        add("usernon");
    }};


    public static final String KEY_ID = "key-hzz66c43688lez2jtgzff";
//    public static final String KEY_ID = "key-hzz6656d2a6x8gzvz0p3s";
    public static final String ALGORITHM = "RSA_PKCS1_SHA_256";
    public static final String TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImQzMDcwODVkNTBiNjRlMjJmZGVjYjFiODE4ZDdhMzUzIn0.******************************************************************************************************************************************************************************************************************************************************************************************";
    public static final String ALIASNAME = "alias/test";

    public static final List<String> VAULT_PATH_LIST = new ArrayList<>() {{
        //00
        add("/user");
        //01
        add("/userkeylack");
        //02
        add("/userkeymore");
        //03
        add("/noexistpath");
        //04
        add("/userkeytypeerror");
    }};
}
