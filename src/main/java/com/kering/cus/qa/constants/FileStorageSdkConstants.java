package com.kering.cus.qa.constants;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class FileStorageSdkConstants {

    public static String DESTINATION = "cus-core-private-nonprod";
//    public static String DESTINATION = "mv-cloud-tt";
    public static String TASK_CENTER_DESTINATION = "cus-core-task-storage-nonprod";
    public static final String NO_DESTINATION = "testnoexist";

    public static final String CONTEXT = "testcatesttestcatesttestcatesttestcatesttestcatest";

    public static final String READ_NOT_EMPTY = "src/main/resources/file/cus/storage/read/testFile.txt";
    public static final String READ_EMPTY = "src/main/resources/file/cus/storage/read/testFileEmpty.txt";
    //非空文件需要通过uploadFile接口上传才可使用，默认为空
    public static byte[] NOT_EMPTY_FILE = null;
    //空文件需要通过uploadFile接口上传才可使用，默认为空
    public static byte[] EMPTY_FILE = null;


    static {
        Optional.ofNullable(System.getenv("STORAGE_OSS_BUCKET")).ifPresent(endpoint ->DESTINATION=endpoint);
    }



    public static final List<String> LOCAL_FILE_DESTINATION_LIST = new ArrayList<>() {{
        //00
        add("src/main/resources/file/cus/storage/write");
        //01
        add("src/main/resources/file/cus/storage/write/noexist");
        //02
        add("src/main/resources/file/cus/storage/write/noexistfile");
        //03
        add("src/main/resources/file/cus/storage/nopowerwrite");
        //04
        add("src/main/resources/file/cus/storage/nopowerread");
    }};

    public static final List<String> PATH_LIST = new ArrayList<>() {{
        //00
        add("exist/testFile.txt");
        //01
        add("testFile.txt");
        //02
        add("file/testFile.txt");
        //03
        add("exist/testFileEmpty.txt");
        //04
        add("noexistfile/testFile.txt");
    }};

    public static final List<String> OSS_LIST = new ArrayList<>() {{
        //00
        add("file/cus/storage/write/exist/testFile.txt");
        //01
        add("file/cus/storage/write/noexist/testFile.txt");
        //02
        add("file/cus/storage/write/noexist/file/testFile.txt");
        //03
        add("file/cus/storage/write/exist/testFileEmpty.txt");
        //04
        add("file/cus/storage/write/noexisttestFile.txt");
        //05
        add("file/cus/storage/write/noexistfile/testFile.txt");
        //06
        add("file/cus/storage/write/exist/qa-test.exe");
    }};
}
