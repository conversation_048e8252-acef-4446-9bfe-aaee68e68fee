package com.kering.cus.qa.constants;

import com.kering.cus.lib.common.PlatformHeaders;
import com.kering.cus.qa.entity.MultiTenantEntity;
import org.springframework.http.HttpHeaders;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

public class MultiTenantSdkConstants {

    public static final Map<String,String> HEADER_MAP = new HashMap<>(){{
        put(PlatformHeaders.TENANT_ID.getValue(),"1111111");
        put(PlatformHeaders.EMPLOYEE_EMAIL.getValue(), "<EMAIL>");
        put(PlatformHeaders.SOURCE.getValue(), "123456789");
        put(PlatformHeaders.CLIENT_ID.getValue(), "0oaewmz2679c5uEea417");
        put(PlatformHeaders.TRACE_ID.getValue(), "123456789");
        put(PlatformHeaders.GALASSIA_REQUEST_ID.getValue(), "67e3551a4af63c71fba586c0ff92d700");
        put(PlatformHeaders.GATEWAY_REQUEST_ID.getValue(), "73859EFC-FF31-4FB6-912F-D2466FFD3D59");
        put(PlatformHeaders.EAGLEEYE_TRACE_ID.getValue(), "123456789");
        put(PlatformHeaders.SUBJECT.getValue(), "123456789");
        put(PlatformHeaders.USER_ID.getValue(), "00ufawzbvm8a5EynD417");
        put(PlatformHeaders.WECOM_EMAIL.getValue(), "com");
        put(PlatformHeaders.ISSUE.getValue(), "https://signin.kering.com/oauth2/default");
    }};

    public static final Consumer<HttpHeaders> OKTA_ALL_HEADERS = header-> {
        header.set(PlatformHeaders.TENANT_ID.getValue(), HEADER_MAP.get(PlatformHeaders.TENANT_ID.getValue()));
        header.set(PlatformHeaders.EMPLOYEE_EMAIL.getValue(), HEADER_MAP.get(PlatformHeaders.EMPLOYEE_EMAIL.getValue()));
        header.set(PlatformHeaders.CLIENT_ID.getValue(), HEADER_MAP.get(PlatformHeaders.CLIENT_ID.getValue()));
        header.set(PlatformHeaders.USER_ID.getValue(), HEADER_MAP.get(PlatformHeaders.USER_ID.getValue()));
        appendHeader(header);
    };


    //TENANT_ID,EMPLOYEE_EMAIL,CLIENT_ID
    public static final Consumer<HttpHeaders> OKTA_PART_HEADERS = header-> {
        appendHeader(header);
        header.set(PlatformHeaders.USER_ID.getValue(), HEADER_MAP.get(PlatformHeaders.USER_ID.getValue()));
    };

    public static void appendHeader(HttpHeaders header){
        header.set(PlatformHeaders.SOURCE.getValue(), HEADER_MAP.get(PlatformHeaders.SOURCE.getValue()));
        header.set(PlatformHeaders.TRACE_ID.getValue(), HEADER_MAP.get(PlatformHeaders.TRACE_ID.getValue()));
        header.set(PlatformHeaders.GALASSIA_REQUEST_ID.getValue(), HEADER_MAP.get(PlatformHeaders.GALASSIA_REQUEST_ID.getValue()));
        header.set(PlatformHeaders.GATEWAY_REQUEST_ID.getValue(), HEADER_MAP.get(PlatformHeaders.GATEWAY_REQUEST_ID.getValue()));
        header.set(PlatformHeaders.EAGLEEYE_TRACE_ID.getValue(), HEADER_MAP.get(PlatformHeaders.EAGLEEYE_TRACE_ID.getValue()));
        header.set(PlatformHeaders.SUBJECT.getValue(), HEADER_MAP.get(PlatformHeaders.SUBJECT.getValue()));
        header.set(PlatformHeaders.WECOM_EMAIL.getValue(), HEADER_MAP.get(PlatformHeaders.WECOM_EMAIL.getValue()));
        header.set(PlatformHeaders.ISSUE.getValue(), HEADER_MAP.get(PlatformHeaders.ISSUE.getValue()));
    }

    public static final Map<String, MultiTenantEntity> MULTI_TENANT_ENTITY_MAP = new HashMap<>();

    public static final String ALL_PARAM_KEY = "all";
    public static final String PART_PARAM_KEY = "part";
    public static final String EMPTY_PARAM_KEY = "empty";

}
