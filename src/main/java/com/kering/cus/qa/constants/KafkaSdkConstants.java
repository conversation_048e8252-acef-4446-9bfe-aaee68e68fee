package com.kering.cus.qa.constants;

public class KafkaSdkConstants {

//    local test
//    public static final String TOPIC_ONE = "MQ_KERING_QUEUE_TEST";
//    public static final String GROUP_ID = "CG_KERING_QUEUE_TEST";
//    public static final String TOPIC_DLT = "MQ_KERING_QUEUE_TEST_DLQ";

    //    dev test
    public static final String TOPIC_ONE = "CUS_PLATFORM_MGMT_QA";
    public static final String GROUP_ID = "CG_Partner_CUS_PLATFORM_QA";
    public static final String TOPIC_DLT = "CUS_PLATFORM_MGMT_QA_DLQ";

//    sasl connect
//    public static final String TOPIC_ONE = "kafkatest";
//    public static final String GROUP_ID = "CG_Partner_CUS_PLATFORM_QA";
//    public static final String TOPIC_DLT = "kafkatest_DLQ";


    public static final String TOPIC_TWO = "topic_test_a";
    public static final String EVENT_TYPE = "eventType";
    public static final String EVENT_TYPE_TEST = "eventTypeTest";
    public static final String EVENT_TYPE_ENCRYPT = "eventTypeEncrypt";
    public static final String EVENT_TYPE_ERROR = "eventTypeError";
    public static final String CONTENT = "testMessage";
    public static final String ORDER_KEY_ONE = "testKey";


    public static final String APP_MESSAGE_TOPIC = "CUS_CORE_INBOX_MESSAGE_DEV";

    public static final String APP_MESSAGE_EVENT_TYPE = "CREATE_MESSAGE";
}
