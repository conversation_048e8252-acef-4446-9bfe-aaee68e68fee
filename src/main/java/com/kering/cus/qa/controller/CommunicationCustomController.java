package com.kering.cus.qa.controller;


import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

@RestController
@ResponseBody
@RequestMapping("/customCommunication")
public class CommunicationCustomController {

    @PostMapping("/testRequest/sleep")
    String sendSleep(HttpServletRequest request, @RequestBody Long time) throws InterruptedException {
        Thread.sleep(time * 1000);
        return "200";
    }
}
