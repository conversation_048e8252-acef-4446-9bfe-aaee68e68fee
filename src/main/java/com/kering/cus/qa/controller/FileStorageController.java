package com.kering.cus.qa.controller;


import com.kering.cus.lib.storage.AssumeRoleService;
import com.kering.cus.lib.storage.StorageService;
import com.kering.cus.lib.storage.dto.AssumedCredential;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.List;
import java.util.Objects;

@RestController
@ResponseBody
@RequestMapping("/file")
@Slf4j
public class FileStorageController {

    @Autowired
    private StorageService storageService;

    @Autowired
    private AssumeRoleService assumeRoleService;

    @Value("${ASSUMED_ROLE_DURATION_SECOND:300}")
    private Long durationSeconds;

    @RequestMapping(value = "/uploadStorageFile")
    public ResponseEntity<String> uploadStorageFile(
            @RequestParam(required = false) String folderName,
            @RequestParam("file") MultipartFile file,
            @RequestParam String bucketName) throws IOException {
        log.info("bucketName = {}", bucketName);
        String path = folderName == null ? file.getOriginalFilename()
                : Path.of(folderName, Objects.requireNonNull(file.getOriginalFilename())).toString();
        String ossPath = storageService.asyncWriteStream(file.getInputStream(), bucketName, path, true);
        return ResponseEntity.ok(ossPath);
    }

    @RequestMapping(value = "/deleteFile")
    public ResponseEntity<String> deleteFile(
            @RequestParam String fileName,
            @RequestParam String bucketName) throws IOException {
        String fileNameDecode = URLDecoder.decode(fileName, StandardCharsets.UTF_8);
        log.info("delete file : {}", fileNameDecode);
        storageService.deleteFile(bucketName, fileNameDecode);
        return ResponseEntity.ok().build();
    }

    @RequestMapping(value = "/deleteFiles")
    public ResponseEntity<List<String>> deleteFiles(
            @RequestParam String fileNames,
            @RequestParam String bucketName) throws IOException {
        String fileNamesDecode = URLDecoder.decode(fileNames, StandardCharsets.UTF_8);
        log.info("delete files : {}", fileNamesDecode);
        return ResponseEntity.ok(
                storageService.deleteFiles(bucketName, List.of(fileNamesDecode.split(","))));
    }

    @RequestMapping(value = "/getStsCredential")
    public ResponseEntity<AssumedCredential> getStsCredential(String sessionName) {
        log.info("getStsCredential sessionName : {}, durationSeconds: {}", sessionName, durationSeconds);
        return ResponseEntity.ok(assumeRoleService.getStsCredential(sessionName));
    }

    @RequestMapping(value = "/getPolicyStsCredential")
    public ResponseEntity<AssumedCredential> getPolicyStsCredential(String sessionName, String policy) {
        log.info("getPolicyStsCredential sessionName : {}, policy = {}", sessionName, policy);
        return ResponseEntity.ok(assumeRoleService.getStsCredential(sessionName, policy));
    }
}
