package com.kering.cus.qa.controller;


import com.kering.cus.qa.constants.CommunicationSdkConstants;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@RestController
@ResponseBody
@RequestMapping("/defaultCommunication")
@Slf4j
public class CommunicationDefaultController {


    @PostMapping("/testRequest/size")
    String sendByteSize(HttpServletRequest request, @RequestBody Integer index){
        return CommunicationSdkConstants.BODY_LIST.get(index);
    }

    @PostMapping("/testRequest/none")
    String sendNoneUrl(@RequestBody String body){
        return "200";
    }

    @PostMapping("/testRequest/sleep")
    String sendSleep(@RequestBody Long time) throws InterruptedException {
        Thread.sleep(time*1000);
        return "200";
    }

    @PostMapping("/testRequest/getHeader")
    Map<String,String> getHeader(HttpServletRequest request){
        Map<String,String> map = new HashMap<>();
        Iterator<String> iterator = request.getHeaderNames().asIterator();
        while(iterator.hasNext()){
            String key = iterator.next();
            map.put(key,request.getHeader(key));
        }
        return map;
    }
}
