package com.kering.cus.qa.controller;


import com.alibaba.fastjson.JSON;
import com.kering.cus.qa.dao.lock.TestOrderLockDao;
import com.kering.cus.qa.entity.PerformanceTestConfig;
import com.kering.cus.qa.entity.RequestMessage;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.entity.TestKafkaMessage;
import com.kering.cus.qa.entity.kafka.AppMessageDTO;
import com.kering.cus.qa.entity.lock.TestOrderEntityLock;
import com.kering.cus.qa.service.base.ExecuteService;
import com.kering.cus.qa.utils.HttpResponse;
import com.kering.cus.qa.utils.HttpclientUtil;
import io.swagger.v3.core.util.Json;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static com.kering.cus.qa.constants.FileStorageSdkConstants.EMPTY_FILE;
import static com.kering.cus.qa.constants.FileStorageSdkConstants.NOT_EMPTY_FILE;

@RestController
@ResponseBody
@RequestMapping("/test")
@Slf4j
public class QaTestController implements InitializingBean {

    private static Date verifyDate;

    @Autowired
    ExecuteService executeService;

    @Autowired
    TestOrderLockDao testOrderLockDao;

    @PostMapping(value = "/run")
    @ResponseBody
    public ResponseEntity run(@RequestBody RequestMessage requestMessage) {

        // Replace the first letter of serviceName with lowercase
        String serviceName = requestMessage.getServiceName().substring(0, 1).toLowerCase() + requestMessage.getServiceName().substring(1);
        return executeService.executeServiceMethod(serviceName, requestMessage.getMethodName(), JSON.toJSONString(requestMessage.getParams()));
    }

    @RequestMapping(value = "/runAll")
    public ResponseEntity runAll(@RequestParam(value = "serviceName") String serviceName) {

        // Replace the first letter of serviceName with lowercase
        serviceName = serviceName.substring(0, 1).toLowerCase() + serviceName.substring(1);
        return executeService.executeServiceMethod(serviceName, null, null);
    }

    @RequestMapping(value = "/verifyDate")
    @ResponseBody
    public ResponseEntity verifyDate() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return ResponseEntity.builder().isSuccess(true).message("Hello World!").data(simpleDateFormat.format(verifyDate)).build();
    }

    @RequestMapping(value = "/uploadFile")
    @ResponseBody
    public ResponseEntity uploadFile(@RequestParam("file") MultipartFile file, @RequestParam String paramName) throws IOException {
        if ("NOT_EMPTY_FILE".equals(paramName)) {
            NOT_EMPTY_FILE = file.getBytes();
        } else if ("EMPTY_FILE".equals(paramName)) {
            EMPTY_FILE = file.getBytes();
        }
        return ResponseEntity.builder().isSuccess(true).message("Hello World!").build();
    }

    @PostMapping(value = "/verifyParam")
    public ResponseEntity verifyParam(@Valid @RequestBody TestKafkaMessage message) throws Exception {
        throw new Exception("test internationalization throw exception");
    }

    /**
     * Fields can be checked or sent
     * @param message
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/sendAppMessage")
    public ResponseEntity sendAppMessage(@Valid @RequestBody AppMessageDTO message) throws Exception {
        return executeService.sendAppMessage(message);
    }

    /**
     * Fields can be checked or sent
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/lock1")
    public ResponseEntity lock1()  {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int updateCount= testOrderLockDao.updateById(testOrderEntity1);
        return ResponseEntity.success(String.valueOf(updateCount));
    }
    /**
     * Fields can be checked or sent
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/lock2")
    public ResponseEntity lock2()  {
        Optional<TestOrderEntityLock> optionalTestOrderEntity2= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity2=optionalTestOrderEntity2.get();
        testOrderEntity2.setVersion(1L);
        int deleteCount= testOrderLockDao.deleteById(testOrderEntity2);
        return ResponseEntity.success(String.valueOf(deleteCount));
    }
    @PostMapping(value = "/performanceTest")
    public ResponseEntity runPerformanceTest(HttpServletRequest request, @RequestBody PerformanceTestConfig performanceTestConfig) {
        ExecutorService executorService = Executors.newFixedThreadPool(performanceTestConfig.getThreadNum());
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();
        long endTime = startTime + performanceTestConfig.getRunTime();
        Enumeration<String> headerNames = request.getHeaderNames();
        Map<String, String> headerMap = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String key = headerNames.nextElement();
            headerMap.put(key, request.getHeader(key));
        }
        performanceTestConfig.setHeaders(headerMap);
        log.info("performanceTestConfig:{}", performanceTestConfig);
        try {
            CountDownLatch latch = new CountDownLatch(performanceTestConfig.getThreadNum());
            while (System.currentTimeMillis() < endTime) {
                executorService.submit(() -> {
                    try {
                        HttpResponse httpResponse;
                        if (performanceTestConfig.getRequestMethod() .equals("POST")) {
                            httpResponse=HttpclientUtil.doPost(performanceTestConfig.getUrl(),performanceTestConfig.getJsonBody());
                        }else {
                            httpResponse=HttpclientUtil.doGet(performanceTestConfig.getUrl());
                        }
                        if (httpResponse==null  ||httpResponse.getCode()!=200){
                            errorCount.incrementAndGet();
                        }
                        successCount.incrementAndGet();
                        latch.countDown();
                    } catch (Exception e) {
                        errorCount.incrementAndGet();
                        log.error("Request failed", e);
                    }
                });
            }
            latch.await();
            return ResponseEntity.success(String.format(
                "Performance test completed - Success: %d, Errors: %d, Duration: %dms",
                successCount.get(), 
                errorCount.get(),
                performanceTestConfig.getRunTime()
            ));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return ResponseEntity.error("Performance test interrupted");
        } finally {
            executorService.shutdown();
        }
    }


    @Override
    public void afterPropertiesSet() {
        verifyDate = new Date();
    }
}
