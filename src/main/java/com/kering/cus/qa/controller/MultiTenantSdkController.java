package com.kering.cus.qa.controller;

import com.kering.cus.lib.common.annotation.RequireEmployeeEmail;
import com.kering.cus.lib.common.annotation.RequireUserId;
import com.kering.cus.lib.common.context.GenericRequestContextHolder;
import com.kering.cus.qa.dao.orm.TestOrderBatisDAO;
import com.kering.cus.qa.entity.MultiTenantEntity;
import com.kering.cus.qa.entity.orm.TestOrderEntity;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import static com.kering.cus.qa.constants.MultiTenantSdkConstants.MULTI_TENANT_ENTITY_MAP;

@RestController
@ResponseBody
@RequestMapping("/multiTenant")
public class MultiTenantSdkController {
    @GetMapping("/test/fullHeader")
    MultiTenantEntity getHeaderInfo(@RequestParam(value = "sceneTypeKey", required = false) String sceneTypeKey, HttpServletRequest request) {
        MultiTenantEntity multiTenantEntity = new MultiTenantEntity();
        if (!StringUtils.isEmpty(sceneTypeKey)) {
            GenericRequestContextHolder.getRequestProperty().ifPresent(multiTenantEntity::setRequestProperty);
            GenericRequestContextHolder.getIdentityContext().ifPresent(multiTenantEntity::setIdentityContext);
            GenericRequestContextHolder.getTraceContext().ifPresent(multiTenantEntity::setTraceContext);
            GenericRequestContextHolder.getTenantContext().ifPresent(multiTenantEntity::setTenantContext);
            Enumeration<String> headerNames = request.getHeaderNames();
            Map<String, String> headerMap = new HashMap<>();
            while (headerNames.hasMoreElements()) {
                String key = headerNames.nextElement();
                headerMap.put(key, request.getHeader(key));
            }
            multiTenantEntity.setHeaderMap(headerMap);
            MULTI_TENANT_ENTITY_MAP.put(sceneTypeKey, multiTenantEntity);
        }
        return multiTenantEntity;
    }

    @RequireEmployeeEmail
    @GetMapping("/test/emailHeader")
    MultiTenantEntity getHeaderInfoEmail(@RequestParam(value = "sceneTypeKey", required = false) String sceneTypeKey, HttpServletRequest request) {
        MultiTenantEntity multiTenantEntity = new MultiTenantEntity();
        if (!StringUtils.isEmpty(sceneTypeKey)) {
            GenericRequestContextHolder.getRequestProperty().ifPresent(multiTenantEntity::setRequestProperty);
            GenericRequestContextHolder.getIdentityContext().ifPresent(multiTenantEntity::setIdentityContext);
            GenericRequestContextHolder.getTraceContext().ifPresent(multiTenantEntity::setTraceContext);
            GenericRequestContextHolder.getTenantContext().ifPresent(multiTenantEntity::setTenantContext);
            Enumeration<String> headerNames = request.getHeaderNames();
            Map<String, String> headerMap = new HashMap<>();
            while (headerNames.hasMoreElements()) {
                String key = headerNames.nextElement();
                headerMap.put(key, request.getHeader(key));
            }
            multiTenantEntity.setHeaderMap(headerMap);
            MULTI_TENANT_ENTITY_MAP.put(sceneTypeKey, multiTenantEntity);
        }
        return multiTenantEntity;
    }


    @RequireUserId
    @GetMapping("/test/userIdHeader")
    MultiTenantEntity getHeaderInfoUserId(@RequestParam(value = "sceneTypeKey", required = false) String sceneTypeKey, HttpServletRequest request) {
        MultiTenantEntity multiTenantEntity = new MultiTenantEntity();
        if (!StringUtils.isEmpty(sceneTypeKey)) {
            GenericRequestContextHolder.getRequestProperty().ifPresent(multiTenantEntity::setRequestProperty);
            GenericRequestContextHolder.getIdentityContext().ifPresent(multiTenantEntity::setIdentityContext);
            GenericRequestContextHolder.getTraceContext().ifPresent(multiTenantEntity::setTraceContext);
            GenericRequestContextHolder.getTenantContext().ifPresent(multiTenantEntity::setTenantContext);
            Enumeration<String> headerNames = request.getHeaderNames();
            Map<String, String> headerMap = new HashMap<>();
            while (headerNames.hasMoreElements()) {
                String key = headerNames.nextElement();
                headerMap.put(key, request.getHeader(key));
            }
            multiTenantEntity.setHeaderMap(headerMap);
            MULTI_TENANT_ENTITY_MAP.put(sceneTypeKey, multiTenantEntity);
        }
        return multiTenantEntity;
    }

    @GetMapping("/test/threadHeader")
    void getHeaderInfoThread(@RequestParam(value = "sceneTypeKey", required = false) String sceneTypeKey, HttpServletRequest request) throws ExecutionException, InterruptedException {
        MultiTenantEntity multiTenantEntity = new MultiTenantEntity();
        GenericRequestContextHolder.getTraceContext().ifPresent(multiTenantEntity::setTraceContext);
        Thread thread =  new Thread(() -> {
            GenericRequestContextHolder.getTraceContext().ifPresent(e -> {
                if (!multiTenantEntity.getTraceContext().getClientId().equals(e.getClientId())) {
                    throw new RuntimeException("clientId is not equals");
                }
            });
        });
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        Future future = executorService.submit(thread);
        future.get();
    }
}
