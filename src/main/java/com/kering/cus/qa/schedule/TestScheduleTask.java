package com.kering.cus.qa.schedule;

import com.kering.cus.lib.scheduler.dynamic.SchedulerTarget;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
@Component
public class TestScheduleTask {

    @SchedulerTarget(name = "ANNOTATION_SCHEDULER")
    public void executeScheduleAnnotation(){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("executeScheduleAnnotation,current time:{}",df.format(new Date()));
    }

    public void executeScheduleTask(){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("executeScheduleTask,current time:{}",df.format(new Date()));
    }

    public void executeParamScheduleTask(Long num){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("executeParamScheduleTask,param num:{},current time:{}",num,df.format(new Date()));
    }

    public void executeParamsScheduleTask(Long num,String type){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("executeParamsScheduleTask,param num:{},type:{},current time:{}",num,type,df.format(new Date()));
    }
}
