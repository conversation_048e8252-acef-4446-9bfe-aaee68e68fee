package com.kering.cus.qa.entity.orm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

@TableName(value = "test_user")
public class TestUserEntity extends MyBatisBaseEntity<String>  implements Serializable {

    @TableField(value = "name")
    private String name;
    @TableField(value = "age")
    private Integer age;
    @TableField(value = "gender")
    private String gender;

    @TableField(value = "birth_date")
    private ZonedDateTime birthDate;


//    public TestUserEntity() {
//        name = RandomUtils.randomString(1, 100);
//        age = RandomUtils.randomNDigitInt(2);
//        gender = UserGenderEnum.Male.name();
//        birthDate = ZonedDateTime.now();
//    }
}
