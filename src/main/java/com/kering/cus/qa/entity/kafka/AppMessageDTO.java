package com.kering.cus.qa.entity.kafka;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppMessageDTO {

    // message id
    private String id;

    // message type
    @NotNull(message = "{app_msg_type_empty}")
    private String type;

    // message title
    @NotBlank(message = "{app_msg_title_empty}")
    private String title;

    // message content
    @NotBlank(message = "{app_msg_content_empty}")
    private String content;

    // message url
    private String url;

    // message receiver
    @NotEmpty(message = "{app_msg_receiver_empty}")
    private List<String> receiver;

    // message source system info
    private SourceSystemInfoDTO sourceSystemInfo;

    // whether the message has been read
    private Boolean isRead;

    //the task id
    private String taskId;

    // message create time
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private ZonedDateTime createTime;

    // message clientId
    @NotBlank(message = "{client_id_empty}")
    private String clientId;
}
