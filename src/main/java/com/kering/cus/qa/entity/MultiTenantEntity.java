package com.kering.cus.qa.entity;

import com.kering.cus.lib.common.context.bo.IdentityContext;
import com.kering.cus.lib.common.context.bo.RequestProperty;
import com.kering.cus.lib.common.context.bo.TenantContext;
import com.kering.cus.lib.common.context.bo.TraceContext;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
public class MultiTenantEntity {


    private RequestProperty requestProperty;
    private IdentityContext identityContext;
    private TraceContext traceContext;
    private TenantContext tenantContext;

    private Map<String,String> headerMap;
}
