package com.kering.cus.qa.entity.orm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "test_find_all")
public class TestFindAllEntity extends MyBatisBaseEntity<String> implements Serializable {
    String name;
    ZonedDateTime nowTime;
    @TableField(value = "index_num_1")
    int indexNum1;
    @TableField(value = "index_num_2")
    int indexNum2;
}
