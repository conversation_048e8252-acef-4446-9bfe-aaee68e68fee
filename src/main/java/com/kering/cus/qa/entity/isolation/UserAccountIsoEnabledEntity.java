package com.kering.cus.qa.entity.isolation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

public class UserAccountIsoEnabledEntity extends MyBatisBaseEntity<String>  implements Serializable {

    @TableField(value = "user_id")
    private String userId;
    @TableField(value = "user_name")
    private String userName;
    @TableField(value = "user_tenant_id")
    private String userTenantId;
    @TableField(value = "acc_id")
    private String accId;
    @TableField(value = "acc_name")
    private String accName;
    @TableField(value = "account_tenant_id")
    private String accountTenantId;
}
