package com.kering.cus.qa.entity.orm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseEntity;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "test_find_page")
public class TestFindPageEntity extends SoftDeleteMyBatisBaseEntity<String> {
    @TableField(value = "name")
    String name;
    @TableField(value = "time")
    ZonedDateTime time;
    @TableField(value = "index_num_1")
    int indexNum1;
}
