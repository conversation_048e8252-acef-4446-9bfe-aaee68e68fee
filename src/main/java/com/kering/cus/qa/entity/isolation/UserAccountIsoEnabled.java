package com.kering.cus.qa.entity.isolation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

@TableName(value = "user_account_iso_enabled")
public class UserAccountIsoEnabled extends MyBatisBaseEntity<String>  implements Serializable {

    @TableField(value = "user_id")
    private String userId;
    @TableField(value = "account_name")
    private String accountName;
}
