package com.kering.cus.qa.entity;

import com.kering.cus.qa.common.TestCaseAnnotation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResponseEntity {

    private Boolean isSuccess;
    private String message;
    private Object caseData;
    private Object data;

    public static ResponseEntity success(String message, Object data) {
        ResponseEntity response = new ResponseEntity();
        response.setIsSuccess(true);
        response.setMessage(message);
        response.setData(data);
        return response;
    }

    public static ResponseEntity success(String message) {
        return success(message, null);
    }

    public static ResponseEntity success() {
        return success(null, null);
    }

    public static ResponseEntity error(String message, Object data) {
        ResponseEntity response = new ResponseEntity();
        response.setIsSuccess(false);
        response.setMessage(message);
        response.setData(data);
        return response;
    }

    public static ResponseEntity error(String message) {
        return error(message, null);
    }

    public static ResponseEntity error22(String message, TestCaseAnnotation testCaseAnnotation) {
        return error(message, testCaseAnnotation.toString());
    }
}
