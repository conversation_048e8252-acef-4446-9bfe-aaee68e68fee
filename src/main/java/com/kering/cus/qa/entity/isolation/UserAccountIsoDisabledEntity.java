package com.kering.cus.qa.entity.isolation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.common.annotation.IgnoreTenantIsolation;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@IgnoreTenantIsolation
@TableName(value = "user_account_iso_disabled")
public class UserAccountIsoDisabledEntity extends MyBatisBaseEntity<String>  implements Serializable {

    private String userId;
    private String userName;
    private String userTenantId;
    private String accId;
    private String accName;
    private String accountTenantId;
}
