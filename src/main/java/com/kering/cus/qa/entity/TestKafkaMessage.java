package com.kering.cus.qa.entity;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

@Data
public class TestKafkaMessage implements Serializable {

    @NotBlank(message = "{test.null.message}")
    private String message;
    private Integer id;

    public TestKafkaMessage() {
    }

    public TestKafkaMessage(String message, Integer id) {
        this.message = message;
        this.id = id;
    }

    @Override
    public String toString() {
        return "TestKafkaMessage{" +
                "message='" + message + '\'' +
                ", id=" + id +
                '}';
    }
}
