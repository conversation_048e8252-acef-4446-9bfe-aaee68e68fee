package com.kering.cus.qa.entity.isolation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

@TableName(value = "user_iso_enabled")
public class UserIsoEnabledEntity extends MyBatisBaseEntity<String>  implements Serializable {

    @TableField(value = "name")
    private String name;
    @TableField(value = "age")
    private Integer age;
}
