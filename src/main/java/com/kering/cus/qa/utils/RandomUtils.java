package com.kering.cus.qa.utils;

import lombok.experimental.UtilityClass;

import java.util.Random;

/**
 * RandomUtils
 * Random tool class
 */
@UtilityClass
public class RandomUtils {

    private static final String CHAR_SET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    //Random bit string
    public static String randomString(int min, int max) {
        Random random = new Random();
        int length = random.nextInt(max - min + 1) + min;
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(CHAR_SET.length());
            sb.append(CHAR_SET.charAt(randomIndex));
        }
        return sb.toString();
    }

    public static int randomNDigitInt(int digitCount) {
        Random random = new Random();
        if (digitCount < 1) {
            throw new IllegalArgumentException("Invalid digit count. It should be greater than 0.");
        }
        int min = (int) Math.pow(10, digitCount - 1); // Minimum value for N digit number
        int max = (int) Math.pow(10, digitCount) - 1; // Maximum value for N digit number
        return random.nextInt(max - min + 1) + min;
    }

}
