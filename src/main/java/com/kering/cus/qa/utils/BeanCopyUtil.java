package com.kering.cus.qa.utils;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;

import java.io.*;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Deep copy, suitable for do and vo interconversion
 *
 * <AUTHOR>
 * @date 2020/12/3 11:23
 */
@Slf4j
@UtilityClass
public class BeanCopyUtil {
    /**
     * Deep copy object
     *
     * @param t   Source object
     * @param <T> Target object
     * @return
     */
    public static <T extends Serializable> T copy(T t) {
        return SerializationUtils.clone(t);
    }

    public static <T extends Serializable> T copyTwo(T t) {
        return t;
    }


    /**
     * Shallow copy of bean. Values with special characters cannot be used
     *
     * @param value
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T copy(Object value, Class<T> clazz) {
        if (isNullOrEmpty(value)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(value), clazz);
    }

    /**
     * The bean special character specifies the object copy, and the generic type in type specifies the complex class
     *
     * @param value
     * @param type
     * @param <T>
     * @return
     */
    public static <T> T typeCopy(Object value, TypeReference<T> type) {
        if (isNullOrEmpty(value)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(value), type);
    }

    /**
     * Shallow copy of array copy. Values with special characters cannot be used
     *
     * @param value
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> collectionCopy(Object value, Class<T> clazz) {
        if (isNullOrEmpty(value)) {
            return null;
        }
        return JSON.parseArray(JSON.toJSONString(value), clazz);
    }

    /**
     * list Deep copy, referring to the object to copy a new object
     *
     * @param sourceList
     * @param <T>
     * @return
     * @throws IOException
     * @throws ClassNotFoundException
     */
    public static <T> List<T> listCopy(List<T> sourceList) throws IOException, ClassNotFoundException {
        ObjectInputStream ois = getInputStream(sourceList);
        @SuppressWarnings("unchecked")
        List<T> result = (List<T>) ois.readObject();
        ois.close();
        return result;
    }

    /**
     * map deep copy, the reference object copies a new object
     *
     * @param sourceMap
     * @param <K>
     * @param <V>
     * @return
     * @throws IOException
     * @throws ClassNotFoundException
     */
    @SuppressWarnings("unchecked")
    public static <K, V> Map<K, V> mapCopy(Map<K, V> sourceMap) {
        try {
            ObjectInputStream ois = getInputStream(sourceMap);
            Map<K, V> result = (Map<K, V>) ois.readObject();
            ois.close();
            return result;
        } catch (IOException | ClassNotFoundException e) {
            log.error("mapCopy error", e);
            return new ConcurrentHashMap<>();
        }
    }

    private static ObjectInputStream getInputStream(Object src) throws IOException {
        //写入字节流
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ObjectOutputStream obs = new ObjectOutputStream(out);
        obs.writeObject(src);
        obs.close();
        ByteArrayInputStream ios = new ByteArrayInputStream(out.toByteArray());
        return new ObjectInputStream(ios);
    }

    public static Boolean copyAttribute(Object from, Object to, Class<?> templateClass) {
        Class<?> fromClass = from.getClass();
        Class<?> toClass = to.getClass();
        Field[] fields = templateClass.getDeclaredFields();
        for (Field f : fields) {
            String fieldName = f.getName();
            String getName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            String setName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            try {
                Method getMethod = fromClass.getDeclaredMethod(getName);
                Method setMethod = toClass.getDeclaredMethod(setName, f.getType());
                setMethod.invoke(to, getMethod.invoke(from));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return true;
    }


    public static boolean isNullOrEmpty(Object value) {
        if (null == value) {
            return true;
        }
        if (value instanceof CharSequence) {
            CharSequence valeuChar = (CharSequence) value;
            int strLen;
            if (valeuChar != null && (strLen = valeuChar.length()) != 0) {
                for (int i = 0; i < strLen; ++i) {
                    if (!Character.isWhitespace(valeuChar.charAt(i))) {
                        return false;
                    }
                }

                return true;
            } else {
                return true;
            }
        }

        if (value instanceof Collection
                || value instanceof Map
                || value instanceof Enumeration
                || value instanceof Iterator
                || value.getClass().isArray()
        ) {
            if (value == null) {
                return true;
            } else if (value instanceof Collection) {
                return ((Collection) value).isEmpty();
            } else if (value instanceof Map) {
                return ((Map) value).isEmpty();
            } else if (value instanceof Object[]) {
                return ((Object[]) ((Object[]) value)).length == 0;
            } else if (value instanceof Iterator) {
                return !((Iterator) value).hasNext();
            } else if (value instanceof Enumeration) {
                return !((Enumeration) value).hasMoreElements();
            } else {
                try {
                    return Array.getLength(value) == 0;
                } catch (IllegalArgumentException var2) {
                    throw new IllegalArgumentException("Unsupported value type: " + value.getClass().getName());
                }
            }
        }
        return false;
    }


}
