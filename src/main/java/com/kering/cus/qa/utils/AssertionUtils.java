package com.kering.cus.qa.utils;

import com.kering.cus.qa.common.AssertException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Asserts the utility class
 * <p>
 * Object - Object value assertion
 * exception assertion
 */
@UtilityClass
@Slf4j
public class AssertionUtils extends Assert {
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter FORMATTER_NO_SECONDS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    // Pass in an executable method, or an exception can be null (don't care about the exception as long as throwing the exception is right)
    public static void assertException(Runnable fun, Class<?> type) throws AssertException {
        try {
            fun.run();
        } catch (Exception e) {
//            log.error("assert exception",e);
            if (type == null || type.isInstance(e)) {
                return;
            }
            throw new AssertException("Exception not met, Assertion failed");
        }
        throw new AssertException("No Exception Thrown , Assertion Failed");
    }

    public static void assertException(Runnable fun) throws AssertException {
        assertException(fun, null);
    }

    /**
     * Asserts whether all public property values of two objects are equal.
     *
     * @param obj1 First object
     * @param obj2 Second object
     */
    public static void assertObjectsEqual(Object obj1, Object obj2, List<String> list) throws AssertException {
        if (!obj1.getClass().equals(obj2.getClass())) {
            throw new AssertException("Objects must be of the same class");
        }
        Class<?> clazz = obj1.getClass();
        Field[] fields = clazz.getDeclaredFields(); // getFields() only returns public fields

        for (Field field : fields) {
            if(!list.contains(field.getName())){
               continue;
            }
            field.setAccessible(true);
            try {
                Object value1 = field.get(obj1);
                Object value2 = field.get(obj2);
                if (value1 instanceof ZonedDateTime && value2 instanceof ZonedDateTime) {
                    value1 = formatDateTime((ZonedDateTime) value1);
                    value2 = formatDateTime((ZonedDateTime) value2);
                }

                Assert.isTrue(value1.equals(value2), "Values do not match for field: " + field.getName());
            } catch (Exception e) {
                throw new AssertException("Failed to access field" + e.getMessage());
            }
        }
    }

    private static String formatDateTime(ZonedDateTime dateTime) {
        return dateTime.format(FORMATTER_NO_SECONDS);
    }

    public static ZonedDateTime convertStringToZonedDateTime(String dateString) {
        // Convert the string to LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.parse(dateString, FORMATTER);
        // Obtain the default time zone of the current system
        ZoneId zoneId = ZoneId.systemDefault();
        // Create ZonedDateTime with the time zone information
        return ZonedDateTime.of(localDateTime, zoneId);
    }

//    public static void main(String[] args) throws InstantiationException, IllegalAccessException {
////        try {
////            assertException(() -> {
////                System.out.println(111);;
////            });
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////        try {
////            assertException(() -> {
////                throw new RuntimeException();
////            });
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
//        TestUserEntity expectedUserEntity = new TestUserEntity();
//        expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
//        expectedUserEntity.setName(RandomUtils.randomString(1, 100));
//        expectedUserEntity.setGender(UserGenderEnum.Male.name());
//        expectedUserEntity.setBirthDate(ZonedDateTime.now());
//
//        TestUserEntity expectedUserEntity2 = BeanCopyUtil.copy(expectedUserEntity);
//        System.out.println(expectedUserEntity2);
//        try {
//            // TODO: The attribute of the parent class cannot be Deleted
//            assertObjectsEqual(expectedUserEntity, expectedUserEntity2);
//        } catch (AssertException e) {
//            e.printStackTrace();
//        }
//    }
}
