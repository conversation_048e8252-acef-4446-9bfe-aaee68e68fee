package com.kering.cus.qa.utils;

import java.io.*;

import static com.kering.cus.qa.constants.FileStorageSdkConstants.EMPTY_FILE;
import static com.kering.cus.qa.constants.FileStorageSdkConstants.NOT_EMPTY_FILE;

public class FileUtils {
    public static InputStream readStreamFromFile(String filePath) {
        try {
            File tempFile = File.createTempFile("temp", null);
            FileOutputStream fos = new FileOutputStream(tempFile);
            byte[] bytes;
            if (filePath.contains("Empty")) {
                bytes = EMPTY_FILE;
            } else {
                bytes = NOT_EMPTY_FILE;
            }
            fos.write(bytes);
            fos.close();
            return new FileInputStream(tempFile);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    public static byte[] readBytesFromFile(String filePath) {
        try (InputStream inputStream = FileUtils.readStreamFromFile(filePath)) {
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            return buffer;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
