package com.kering.cus.qa.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.http.*;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.core5.util.Timeout;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;

@Slf4j
public class HttpclientUtil {
    private static final CloseableHttpClient httpClient;

    static {
        // 使用连接池管理高并发场景下的连接
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(200); // 最大连接数
        connectionManager.setDefaultMaxPerRoute(20); // 每个路由最大连接数
        
        // 设置超时配置
        RequestConfig config = RequestConfig.custom()
                .setConnectionRequestTimeout(Timeout.of(Duration.ofSeconds(60))) // 请求超时时间
                .setResponseTimeout(Timeout.of(Duration.ofSeconds(30)))
                .build();

        httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(config)
                .build();
    }

    public static HttpResponse doGet(String url) {
        HttpGet httpGet = new HttpGet(url);
        try {
            return httpClient.execute(httpGet, HttpclientUtil::handleResponse);
        } catch (IOException e) {
            log.error("http get error", e);
            return null;
        }
    }

    public static HttpResponse doPost(String url, String jsonBody) {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(new StringEntity(jsonBody, ContentType.APPLICATION_JSON));
        try {
            return httpClient.execute(httpPost, HttpclientUtil::handleResponse);
        } catch (IOException e) {
            log.error("http post error", e);
            return null;
        }
    }

    private static HttpResponse handleResponse(ClassicHttpResponse response) {
        HttpEntity entity = response.getEntity();
        HttpResponse httpResponse = HttpResponse.builder()
                .code(response.getCode())
                .headers(response.getHeaders())
               .build();
        try {
            httpResponse.setBody(entity != null ? EntityUtils.toString(entity, StandardCharsets.UTF_8) : null);
            return httpResponse;
        } catch (ParseException | IOException e) {
            return httpResponse;
        }
    }
}