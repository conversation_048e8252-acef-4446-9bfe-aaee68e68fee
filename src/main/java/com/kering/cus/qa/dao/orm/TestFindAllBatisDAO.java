package com.kering.cus.qa.dao.orm;


import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.orm.TestFindAllEntity;
import com.kering.cus.qa.mapper.orm.TestFindAllMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class TestFindAllBatisDAO extends MyBatisBaseDAO<TestFindAllEntity, TestFindAllMapper, String> {
    @Autowired
    TestFindAllMapper testFindAllMapper;

    public int deleteAll() {
        return testFindAllMapper.deleteAll();
    }
}
