package com.kering.cus.qa.dao.orm;

import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.orm.TestUserEntity;
import com.kering.cus.qa.mapper.orm.TestUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public class TestUserBatisDAO extends MyBatisBaseDAO<TestUserEntity, TestUserMapper, String> {
    @Autowired
    TestUserMapper testUserMapper;

    public int deleteAll() {
        return testUserMapper.deleteAll();
    }

    public Optional<TestUserEntity> findById(String id) {
        return super.findById(id);
    }
}
