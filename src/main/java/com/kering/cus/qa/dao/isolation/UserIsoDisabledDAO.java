package com.kering.cus.qa.dao.isolation;


import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.isolation.UserIsoDisabledEntity;
import com.kering.cus.qa.mapper.isolation.UserIsoDisabledMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class UserIsoDisabledDAO extends MyBatisBaseDAO<UserIsoDisabledEntity, UserIsoDisabledMapper, String> {
    @Autowired
    UserIsoDisabledMapper mapper;

    public int deleteAll() {
        return mapper.deleteAll();
    }

    public int insertUserIsoDisabled(UserIsoDisabledEntity entity) {
        return mapper.insertUserIsoDisabled(entity);
    }

    public UserIsoDisabledEntity selectUserIsoDisabledById(String id) {
        return mapper.selectUserIsoDisabledById(id);

    }
}
