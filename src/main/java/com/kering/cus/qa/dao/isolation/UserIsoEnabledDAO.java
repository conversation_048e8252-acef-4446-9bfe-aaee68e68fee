package com.kering.cus.qa.dao.isolation;


import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.isolation.UserIsoEnabledEntity;
import com.kering.cus.qa.mapper.isolation.UserIsoEnabledMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UserIsoEnabledDAO extends MyBatisBaseDAO<UserIsoEnabledEntity, UserIsoEnabledMapper, String> {
    @Autowired
    UserIsoEnabledMapper mapper;

    public int deleteAll() {
        return mapper.deleteAll();
    }
    public int insertUserIsoEnabled(UserIsoEnabledEntity entity) {
        return mapper.insertUserIsoEnabled(entity);
    }

    public int updateUserIsoEnabledById(UserIsoEnabledEntity entity) {
        return mapper.updateUserIsoEnabledById(entity);
    }

    public int updateUserIsoEnabledByName(UserIsoEnabledEntity userIsoEnabledEntity) {
        return mapper.updateUserIsoEnabledByName(userIsoEnabledEntity);

    }

    public int deleteUserIsoEnabledById(String id) {
        return mapper.deleteUserIsoEnabledById(id);
    }

    public UserIsoEnabledEntity selectUserIsoEnabledById(String id) {
        return mapper.selectUserIsoEnabledById(id);
    }

    public List<UserIsoEnabledEntity> selectUserIsoEnabledByTenantId(String tenantId) {
        return mapper.selectUserIsoEnabledByTenantId(tenantId);
    }

    public UserIsoEnabledEntity selectUserIsoEnabledByName(String name) {
        return mapper.selectUserIsoEnabledByName(name);
    }
}
