package com.kering.cus.qa.dao.isolation;


import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.isolation.UserAccountEnabledDisabledEntity;
import com.kering.cus.qa.entity.isolation.UserAccountIsoEnabledEntity;
import com.kering.cus.qa.entity.isolation.UserIsoDisabledEntity;
import com.kering.cus.qa.mapper.isolation.UserAccountIsoEnabledMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UserAccountIsoEnabledDAO extends MyBatisBaseDAO<UserAccountIsoEnabledEntity, UserAccountIsoEnabledMapper, String> {
    @Autowired
    UserAccountIsoEnabledMapper mapper;

    public int deleteAll() {
        return mapper.deleteAll();
    }

    public List<UserAccountIsoEnabledEntity> selectUserAccountEntityByUserId(String id) {
        return mapper.selectUserAccountEntityByUserId(id);
    }

    public List<UserAccountIsoEnabledEntity> selectUserAccountEntityByUserId2(String id) {
        return mapper.selectUserAccountEntityByUserId2(id);
    }
}
