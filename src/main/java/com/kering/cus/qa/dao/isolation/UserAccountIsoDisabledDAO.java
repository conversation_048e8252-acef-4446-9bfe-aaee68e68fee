package com.kering.cus.qa.dao.isolation;


import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.isolation.UserAccountIsoDisabledEntity;
import com.kering.cus.qa.mapper.isolation.UserAccountIsoDisabledMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UserAccountIsoDisabledDAO extends MyBatisBaseDAO<UserAccountIsoDisabledEntity, UserAccountIsoDisabledMapper, String> {
    @Autowired
    UserAccountIsoDisabledMapper mapper;

    public int deleteAll() {
        return mapper.deleteAll();
    }

    public List<UserAccountIsoDisabledEntity> selectUserDisabledAccountDisabledEntityByUserId(String id) {
        return mapper.selectUserDisabledAccountDisabledEntityByUserId(id);
    }

    public List<UserAccountIsoDisabledEntity> selectUserDisabledAccountDisabledEntityByUserId2(String id) {
        return mapper.selectUserDisabledAccountDisabledEntityByUserId2(id);
    }
}
