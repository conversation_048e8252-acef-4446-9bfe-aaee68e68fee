package com.kering.cus.qa.dao.isolation;


import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.isolation.UserAccountEnabledDisabledEntity;
import com.kering.cus.qa.mapper.isolation.UserAccountEnabledDisabledMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UserAccountEnabledDisabledDAO extends MyBatisBaseDAO<UserAccountEnabledDisabledEntity, UserAccountEnabledDisabledMapper, String> {
    @Autowired
    UserAccountEnabledDisabledMapper mapper;

    public int deleteAll() {
        return mapper.deleteAll();
    }

    public List<UserAccountEnabledDisabledEntity> selectUserEnabledAccountDisabledEntityByUserId(String id) {
        return mapper.selectUserEnabledAccountDisabledEntityByUserId(id);
    }

    public List<UserAccountEnabledDisabledEntity> selectUserEnabledAccountDisabledEntityByUserId2(String id) {
        return mapper.selectUserEnabledAccountDisabledEntityByUserId2(id);
    }
}
