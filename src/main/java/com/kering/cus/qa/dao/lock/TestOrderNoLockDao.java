package com.kering.cus.qa.dao.lock;

import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.lock.TestOrderEntityLock;
import com.kering.cus.qa.entity.lock.TestOrderEntityNoLock;
import com.kering.cus.qa.entity.orm.TestOrderEntity;
import com.kering.cus.qa.mapper.lock.TestOrderNoLockMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class TestOrderNoLockDao extends MyBatisBaseDAO<TestOrderEntityNoLock, TestOrderNoLockMapper, Integer> {

    @Autowired
    TestOrderNoLockMapper testOrderNoLockMapper;

    public int updateById(TestOrderEntityNoLock entity) {
        return testOrderNoLockMapper.updateById(entity);
    }
    public Integer updatePriceByWhere(TestOrderEntityNoLock entity, String price) {
        return testOrderNoLockMapper.updatePriceByWhere(entity, price);
    }
    public Integer updatePriceByVersionEqual(TestOrderEntityNoLock entity, Integer version) {
        return testOrderNoLockMapper.updatePriceByVersionEqual(entity, version);
    }
    public Integer updatePriceByVersionGreaterThan(TestOrderEntityNoLock entity, Integer version) {
        return testOrderNoLockMapper.updatePriceByVersionGreaterThan(entity, version);
    }
    public Integer updatePriceByVersionLessThan(TestOrderEntityNoLock entity, Integer version) {
        return testOrderNoLockMapper.updatePriceByVersionLessThan(entity, version);
    }

    public int deleteById(TestOrderEntityNoLock entity) {
        return testOrderNoLockMapper.deleteById(entity);
    }
    public Integer deletePriceByWhere(TestOrderEntityNoLock entity, String price) {
        return testOrderNoLockMapper.deletePriceByWhere(entity, price);
    }
    public Integer deletePriceByVersionEqual(TestOrderEntityNoLock entity, Integer version) {
        return testOrderNoLockMapper.deletePriceByVersionEqual(entity, version);
    }
    public Integer deletePriceByVersionGreaterThan(TestOrderEntityNoLock entity, Integer version) {
        return testOrderNoLockMapper.deletePriceByVersionGreaterThan(entity, version);
    }
    public Integer deletePriceByVersionLessThan(TestOrderEntityNoLock entity, Integer version) {
        return testOrderNoLockMapper.deletePriceByVersionLessThan(entity, version);
    }

}
