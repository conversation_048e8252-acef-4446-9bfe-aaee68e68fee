package com.kering.cus.qa.dao.lock;

import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.lock.TestOrderEntityLock;
import com.kering.cus.qa.entity.orm.TestOrderEntity;
import com.kering.cus.qa.mapper.lock.TestOrderLockMapper;
import com.kering.cus.qa.mapper.orm.TestOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class TestOrderLockDao extends MyBatisBaseDAO<TestOrderEntityLock, TestOrderLockMapper, Integer> {

    @Autowired
    TestOrderLockMapper testorderLockMapper;

    public int updateById(TestOrderEntityLock entity) {
        return testorderLockMapper.updateById(entity);
    }

    public Integer updatePriceByWhere(TestOrderEntityLock entity, String price) {
        return testorderLockMapper.updatePriceByWhere(entity, price);
    }
    public Integer updatePriceByVersionEqual(TestOrderEntityLock entity, Integer version) {
        return testorderLockMapper.updatePriceByVersionEqual(entity, version);
    }
    public Integer updatePriceByVersionGreaterThan(TestOrderEntityLock entity, Integer version) {
        return testorderLockMapper.updatePriceByVersionGreaterThan(entity, version);
    }
    public Integer updatePriceByVersionLessThan(TestOrderEntityLock entity, Integer version) {
        return testorderLockMapper.updatePriceByVersionLessThan(entity, version);
    }

    public Integer deleteById(TestOrderEntityLock entity) {
        return testorderLockMapper.deleteById(entity);
    }

    public Integer deletePriceByWhere(TestOrderEntityLock entity, String price) {
        return testorderLockMapper.deletePriceByWhere(entity, price);
    }
    public Integer deletePriceByVersionEqual(TestOrderEntityLock entity, Integer version) {
        return testorderLockMapper.deletePriceByVersionEqual(entity, version);
    }
    public Integer deletePriceByVersionGreaterThan(TestOrderEntityLock entity, Integer version) {
        return testorderLockMapper.deletePriceByVersionGreaterThan(entity, version);
    }
    public Integer deletePriceByVersionLessThan(TestOrderEntityLock entity, Integer version) {
        return testorderLockMapper.deletePriceByVersionLessThan(entity, version);
    }

}
