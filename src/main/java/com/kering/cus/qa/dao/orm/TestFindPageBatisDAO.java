package com.kering.cus.qa.dao.orm;


import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.orm.TestFindPageEntity;
import com.kering.cus.qa.mapper.orm.TestFindPageMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class TestFindPageBatisDAO extends MyBatisBaseDAO<TestFindPageEntity, TestFindPageMapper, String> {
    @Autowired
    TestFindPageMapper testFindPageMapper;

    public int deleteAll() {
        return testFindPageMapper.deleteAll();
    }
}
