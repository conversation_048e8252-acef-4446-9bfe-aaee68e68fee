package com.kering.cus.qa.dao.orm;

import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.orm.TestOrderEntity;
import com.kering.cus.qa.entity.sqlCondition.SqlCondition;
import com.kering.cus.qa.mapper.orm.TestOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Repository
public class TestOrderBatisDAO extends MyBatisBaseDAO<TestOrderEntity, TestOrderMapper, Integer> {
    @Autowired
    TestOrderMapper testOrderMapper;

    public int deleteId(Serializable id) {
        return testOrderMapper.deleteId(id);
    }

    public int deleteAll() {
        return testOrderMapper.deleteAll();
    }
    public int updateById(TestOrderEntity entity) {
        return testOrderMapper.updateById(entity);
    }
    public int updateByCondition(TestOrderEntity entity, List<SqlCondition> sqlConditionList) {
        return testOrderMapper.updateByCondition(entity, sqlConditionList);
    }
}
