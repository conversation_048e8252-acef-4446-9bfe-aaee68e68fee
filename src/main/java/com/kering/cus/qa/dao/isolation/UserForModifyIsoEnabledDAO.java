package com.kering.cus.qa.dao.isolation;


import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.qa.entity.isolation.UserForModifyIsoEnabledEntity;
import com.kering.cus.qa.mapper.isolation.UserForModifyIsoEnabledMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class UserForModifyIsoEnabledDAO extends MyBatisBaseDAO<UserForModifyIsoEnabledEntity, UserForModifyIsoEnabledMapper, String> {
    @Autowired
    UserForModifyIsoEnabledMapper mapper;

    public int deleteAll() {
        return mapper.deleteAll();
    }


    public int updateUserForModifyIsoEnabledByTenantId(UserForModifyIsoEnabledEntity entity) {
        return mapper.updateUserForModifyIsoEnabledByTenantId(entity);
    }
    public int deleteUserForModifyIsoEnabledByTenantId(String tenantId) {
        return mapper.deleteUserForModifyIsoEnabledByTenantId(tenantId);
    }

    public int deleteUserForModifyIsoEnabledByName(String name) {
        return mapper.deleteUserForModifyIsoEnabledByName(name);
    }
}
