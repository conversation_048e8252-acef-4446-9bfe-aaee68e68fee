package com.kering.cus.qa.interceptor;

import com.kering.cus.lib.common.util.ApplicationContextUtil;
import com.kering.cus.lib.message.queue.config.KafkaProducerProperties;
import com.kering.cus.lib.message.queue.producer.dto.GenericEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerInterceptor;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;

import static com.kering.cus.qa.constants.KafkaSdkConstants.CONTENT;
import static com.kering.cus.qa.constants.KafkaSdkConstants.EVENT_TYPE_ENCRYPT;

@Slf4j
@Component
@Order(301)
@DependsOn("applicationConfig")
public class CustomProducerInterceptor implements ProducerInterceptor<String, GenericEvent<?>> {
    @Override
    public ProducerRecord onSend(ProducerRecord producerRecord) {
        KafkaProducerProperties kafkaProducerProperties = ApplicationContextUtil.getBean(KafkaProducerProperties.class);
        if (kafkaProducerProperties.getEncryptEnabled()) {
            GenericEvent<Object> event = (GenericEvent) producerRecord.value();
            Assert.isTrue(!CONTENT.equals(event.getData().toString()), "Encrypt data not equals");
        }
        if (kafkaProducerProperties.getEncryptEnabled()) {
            GenericEvent<Object> event = (GenericEvent) producerRecord.value();
            if (EVENT_TYPE_ENCRYPT.equals(event.getEventType())) {
                event.setData(event.getData() + "1");
            }
        }
        String traceId = new String(producerRecord.headers().lastHeader("issueBy").value());
        Assert.hasText(traceId, "issueBy not empty");
        return producerRecord;
    }

    @Override
    public void onAcknowledgement(RecordMetadata recordMetadata, Exception e) {

    }

    @Override
    public void close() {

    }

    @Override
    public void configure(Map<String, ?> map) {

    }
}
