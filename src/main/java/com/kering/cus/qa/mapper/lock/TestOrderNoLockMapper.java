package com.kering.cus.qa.mapper.lock;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.qa.entity.lock.TestOrderEntityNoLock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TestOrderNoLockMapper extends BaseMapper<TestOrderEntityNoLock> {
    int updatePriceByWhere(@Param("entity")TestOrderEntityNoLock entity, @Param("price")String price);
    int updatePriceByVersionEqual(@Param("entity")TestOrderEntityNoLock entity,@Param("version")Integer version);
    int updatePriceByVersionGreaterThan(@Param("entity")TestOrderEntityNoLock entity,@Param("version")Integer version);
    int updatePriceByVersionLessThan(@Param("entity")TestOrderEntityNoLock entity,@Param("version")Integer version);


    int deletePriceByWhere(@Param("entity")TestOrderEntityNoLock entity, @Param("price")String price);
    int deletePriceByVersionEqual(@Param("entity")TestOrderEntityNoLock entity,@Param("version")Integer version);
    int deletePriceByVersionGreaterThan(@Param("entity")TestOrderEntityNoLock entity,@Param("version")Integer version);
    int deletePriceByVersionLessThan(@Param("entity")TestOrderEntityNoLock entity,@Param("version")Integer version);


}
