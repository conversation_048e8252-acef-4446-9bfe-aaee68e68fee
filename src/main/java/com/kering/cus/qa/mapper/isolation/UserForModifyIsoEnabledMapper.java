package com.kering.cus.qa.mapper.isolation;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.qa.entity.isolation.UserForModifyIsoEnabledEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserForModifyIsoEnabledMapper extends BaseMapper<UserForModifyIsoEnabledEntity> {
    Integer deleteAll();

    int updateUserForModifyIsoEnabledByTenantId(UserForModifyIsoEnabledEntity entity);

    int deleteUserForModifyIsoEnabledByTenantId(String tenantId);

    int deleteUserForModifyIsoEnabledByName(String name);
}
