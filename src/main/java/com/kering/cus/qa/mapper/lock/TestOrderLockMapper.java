package com.kering.cus.qa.mapper.lock;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.qa.entity.lock.TestOrderEntityLock;
import com.kering.cus.qa.entity.orm.TestOrderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TestOrderLockMapper extends BaseMapper<TestOrderEntityLock> {
    int updatePriceByWhere(@Param("entity") TestOrderEntityLock entity,@Param("price")  String price);
    int updatePriceByVersionEqual(@Param("entity") TestOrderEntityLock entity,@Param("version") Integer version);
    int updatePriceByVersionGreaterThan(@Param("entity") TestOrderEntityLock entity,@Param("version") Integer version);
    int updatePriceByVersionLessThan(@Param("entity") TestOrderEntityLock entity,@Param("version") Integer version);
    int deletePriceByWhere(@Param("entity") TestOrderEntityLock entity,@Param("price")  String price);
    int deletePriceByVersionEqual(@Param("entity") TestOrderEntityLock entity,@Param("version") Integer version);
    int deletePriceByVersionGreaterThan(@Param("entity") TestOrderEntityLock entity,@Param("version") Integer version);
    int deletePriceByVersionLessThan(@Param("entity") TestOrderEntityLock entity,@Param("version") Integer version);


}
