package com.kering.cus.qa.mapper.isolation;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.qa.entity.isolation.UserIsoDisabledEntity;
import com.kering.cus.qa.entity.isolation.UserIsoEnabledEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserIsoDisabledMapper extends BaseMapper<UserIsoDisabledEntity> {
    Integer deleteAll();

    int insertUserIsoDisabled(UserIsoDisabledEntity entity);

    UserIsoDisabledEntity selectUserIsoDisabledById(String id);
}
