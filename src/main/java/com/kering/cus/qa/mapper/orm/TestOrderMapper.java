package com.kering.cus.qa.mapper.orm;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.qa.entity.orm.TestOrderEntity;
import com.kering.cus.qa.entity.sqlCondition.SqlCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Mapper
public interface TestOrderMapper extends BaseMapper<TestOrderEntity>{

    Integer deleteId(Serializable id);

    Integer deleteAll();

    Integer updateByCondition(TestOrderEntity entity, List<SqlCondition> sqlConditionList);
}