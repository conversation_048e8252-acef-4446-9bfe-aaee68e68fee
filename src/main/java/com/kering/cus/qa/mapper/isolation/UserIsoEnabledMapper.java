package com.kering.cus.qa.mapper.isolation;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.qa.entity.isolation.UserIsoEnabledEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UserIsoEnabledMapper extends BaseMapper<UserIsoEnabledEntity> {
    Integer deleteAll();

    int insertUserIsoEnabled(UserIsoEnabledEntity entity);

    int updateUserIsoEnabledById(UserIsoEnabledEntity entity);

    int updateUserIsoEnabledByName(UserIsoEnabledEntity userIsoEnabledEntity);

    int deleteUserIsoEnabledById(String id);

    UserIsoEnabledEntity selectUserIsoEnabledById(String id);

    List<UserIsoEnabledEntity> selectUserIsoEnabledByTenantId(String tenantId);

    UserIsoEnabledEntity selectUserIsoEnabledByName(String name);
}
