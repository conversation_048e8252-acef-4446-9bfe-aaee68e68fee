package com.kering.cus.qa.mapper.isolation;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.qa.entity.isolation.UserAccountIsoDisabledEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserAccountIsoDisabledMapper extends BaseMapper<UserAccountIsoDisabledEntity> {
    Integer deleteAll();

    List<UserAccountIsoDisabledEntity> selectUserDisabledAccountDisabledEntityByUserId(@Param("id")String id);

    List<UserAccountIsoDisabledEntity> selectUserDisabledAccountDisabledEntityByUserId2(@Param("id")String id);
}
