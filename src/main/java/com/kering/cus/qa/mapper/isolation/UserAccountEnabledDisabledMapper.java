package com.kering.cus.qa.mapper.isolation;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.qa.entity.isolation.UserAccountEnabledDisabledEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserAccountEnabledDisabledMapper extends BaseMapper<UserAccountEnabledDisabledEntity> {
    Integer deleteAll();

    List<UserAccountEnabledDisabledEntity> selectUserEnabledAccountDisabledEntityByUserId(@Param("id")String id);

    List<UserAccountEnabledDisabledEntity> selectUserEnabledAccountDisabledEntityByUserId2(@Param("id")String id);
}
