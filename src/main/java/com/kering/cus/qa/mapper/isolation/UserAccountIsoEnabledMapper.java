package com.kering.cus.qa.mapper.isolation;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.qa.entity.isolation.UserAccountIsoEnabledEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserAccountIsoEnabledMapper extends BaseMapper<UserAccountIsoEnabledEntity> {
    Integer deleteAll();

    List<UserAccountIsoEnabledEntity> selectUserAccountEntityByUserId(@Param("id")String id);

    List<UserAccountIsoEnabledEntity> selectUserAccountEntityByUserId2(@Param("id")String id);
}
