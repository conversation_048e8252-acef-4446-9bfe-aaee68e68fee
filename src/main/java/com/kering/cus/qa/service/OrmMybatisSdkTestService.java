
package com.kering.cus.qa.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kering.cus.lib.common.SortCriteria;
import com.kering.cus.lib.persistence.common.entity.PageResult;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseEntity;
import com.kering.cus.qa.common.AssertException;
import com.kering.cus.qa.common.TestCaseAnnotation;
import com.kering.cus.qa.dao.orm.TestFindAllBatisDAO;
import com.kering.cus.qa.dao.orm.TestFindPageBatisDAO;
import com.kering.cus.qa.dao.orm.TestOrderBatisDAO;
import com.kering.cus.qa.dao.orm.TestUserBatisDAO;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.entity.orm.*;
import com.kering.cus.qa.entity.sqlCondition.ConditionType;
import com.kering.cus.qa.entity.sqlCondition.SqlCondition;
import com.kering.cus.qa.utils.AssertionUtils;
import com.kering.cus.qa.utils.BeanCopyUtil;
import com.kering.cus.qa.utils.RandomUtils;
import com.kering.cus.qa.utils.ThrowableUtils;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

import static com.kering.cus.qa.constants.MessageConstants.THROW_EXCEPTION;
import static com.kering.cus.qa.constants.MybatisSdkConstants.DEFAULT_USER;
import static com.kering.cus.qa.constants.MybatisSdkConstants.DEV;
import static com.kering.cus.qa.utils.AssertionUtils.convertStringToZonedDateTime;

/**
 * Test the service class of the ORM Mybatis SDK
 * design documentation:
 * test case documentation:
 */

@Service
public class OrmMybatisSdkTestService {

    @Autowired
    private TestUserBatisDAO testUserDAO;
    @Autowired
    private TestOrderBatisDAO testOrderDAO;
    @Autowired
    private TestFindAllBatisDAO testFindAllDAO;
    @Autowired
    private TestFindPageBatisDAO testFindPageDAO;
    @Autowired
    private HikariDataSource hikariDataSource;

    public ResponseEntity initBasicData() {
        try {

            testUserDAO.deleteAll();
            testOrderDAO.deleteAll();
            testFindPageDAO.deleteAll();

            // ----------------------- user ------------------------------
            TestUserEntity expectedUserEntity1 = new TestUserEntity();
            expectedUserEntity1.setAge(RandomUtils.randomNDigitInt(2));
            expectedUserEntity1.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity1.setGender(UserGenderEnum.Male.name());
            expectedUserEntity1.setBirthDate(ZonedDateTime.now());
            testUserDAO.create(expectedUserEntity1, "create_test_id");

            TestUserEntity expectedUserEntity2 = new TestUserEntity();
            expectedUserEntity2.setAge(11);
            expectedUserEntity2.setName("find_test_name");
            expectedUserEntity2.setGender(UserGenderEnum.Male.name());
            expectedUserEntity2.setBirthDate(convertStringToZonedDateTime("2024-06-25 17:50:33"));
            testUserDAO.create(expectedUserEntity2, "find_test_id");

            TestUserEntity expectedUserEntity3 = new TestUserEntity();
            expectedUserEntity3.setAge(RandomUtils.randomNDigitInt(2));
            expectedUserEntity3.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity3.setGender(UserGenderEnum.Male.name());
            expectedUserEntity3.setBirthDate(ZonedDateTime.now());
            testUserDAO.create(expectedUserEntity3, "update_test_id");

            // ----------------------- order ------------------------------
            TestOrderEntity testOrderEntity1 = new TestOrderEntity();
            testOrderEntity1.setPrice("11.22");
            testOrderDAO.create(testOrderEntity1, 1);

            TestOrderEntity testOrderEntity2 = new TestOrderEntity();
            testOrderEntity2.setPrice("21.22");
            testOrderDAO.create(testOrderEntity2, 2);

            TestOrderEntity testOrderEntity3 = new TestOrderEntity();
            testOrderEntity3.setPrice(String.valueOf(RandomUtils.randomNDigitInt(3)));
            testOrderDAO.create(testOrderEntity3, 3);
            testOrderDAO.delete(testOrderEntity3);
            // ----------------------- find page ------------------------------
            for (int i = 1; i <= 21; i++) {
                TestFindPageEntity testFindPageEntity = new TestFindPageEntity();
                testFindPageEntity.setName("test");
                testFindPageEntity.setIndexNum1(i);
                testFindPageEntity.setTime(convertStringToZonedDateTime("2024-06-25 10:10:10"));
                if (i == 21) {
                    testFindPageEntity.setTime(convertStringToZonedDateTime("2024-05-25 10:10:10"));
                }
                testFindPageDAO.create(testFindPageEntity);
            }

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success("init data success");
    }

    public ResponseEntity findUserAll() {
        return ResponseEntity.success("findUserAll", testUserDAO.findAll(1, 1000, null));
    }

    public ResponseEntity findOrderAll() {
        return ResponseEntity.success("findOrderAll", testOrderDAO.findAll(1, 1000, null));
    }

    public ResponseEntity findAllAll() {
        SortCriteria sortCriteria = new SortCriteria();
        sortCriteria.getCriteria().put("index_num_1", "asc");
        return ResponseEntity.success("findUserAll", testFindAllDAO.findAll(1, 2000, sortCriteria));
    }

    public ResponseEntity findPageAll() {
        SortCriteria sortCriteria = new SortCriteria();
        sortCriteria.getCriteria().put("index_num_1", "asc");
        return ResponseEntity.success("findUserAll", testFindPageDAO.findAll(1, 2000, sortCriteria));
    }

    public ResponseEntity initFindAllData() {
        try {
            testFindAllDAO.deleteAll();

            // ----------------------- find all ------------------------------
            int indexNum2 = 1;
            for (int i = 1; i <= 1001; i++) {
                TestFindAllEntity testFindAllEntity = new TestFindAllEntity();
                testFindAllEntity.setName(RandomUtils.randomString(1, 50));
                testFindAllEntity.setIndexNum1(i);
                testFindAllEntity.setIndexNum2(i % 10 == 0 ? indexNum2++ : indexNum2);
                testFindAllDAO.create(testFindAllEntity);
                Thread.sleep(30);
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    /**
     * @param timeToCheck
     * @return
     */
    public static boolean isWithinOneMinute(ZonedDateTime timeToCheck) {
        ZonedDateTime oneMinuteAgo = ZonedDateTime.now(ZoneId.systemDefault()).minusMinutes(1);
        return timeToCheck.isAfter(oneMinuteAgo) && timeToCheck.isBefore(ZonedDateTime.now(ZoneId.systemDefault()).plusSeconds(1));
    }

    /**
     * Assertion A field that is modified by default
     *
     * @param type   create  update
     * @param entity
     * @throws AssertException
     */
    public static void assertDefaultFields(String type, MyBatisBaseEntity entity) throws AssertException {
        if ("create".equals(type)) {
            Assert.isTrue(DEFAULT_USER.equals(entity.getCreatedBy()), "Create Data:CreateBy should be defaultUser");
            Assert.isTrue(DEFAULT_USER.equals(entity.getModifiedBy()), "Create Data:ModifiedBy should be defaultUser");
            Assert.isTrue(isWithinOneMinute(entity.getCreatedDate()), "Create Data:CreatedDate should in one minute");
            Assert.isTrue(isWithinOneMinute(entity.getModifiedDate()), "Create Data:ModifiedDate should be in one minute");
        } else if ("update".equals(type)) {
            Assert.isTrue(DEFAULT_USER.equals(entity.getModifiedBy()), "Update Data:ModifiedBy should be defaultUser");
            Assert.isTrue(isWithinOneMinute(entity.getModifiedDate()), "Update Data:ModifiedDate should in one minute");
        } else {
            throw new AssertException("Unknown type: " + type);
        }
    }

    @TestCaseAnnotation(
            testSet = "create(E entity) : E",
            summary = "Create new entity data into the database as normal",
            caseNumber = "tc001"
    )
    public ResponseEntity tc001() throws Exception {
//        try {
        // -------- test action start --------------
            /* 1.Create: A new entity object entity= TestUserEntity<String> {
                name: "Random 1 - 100 characters",
                age: "Random numbers from 1 to 2",
                gender: UserGenderEnum.Male,
                birthDate: ZonedDateTime.now()
            } */
        TestUserEntity expectedUserEntity = new TestUserEntity();
        expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
        expectedUserEntity.setName(RandomUtils.randomString(1, 100));
        expectedUserEntity.setGender(UserGenderEnum.Male.name());
        expectedUserEntity.setBirthDate(ZonedDateTime.now());
        //2.Invoke: Crete (Entit) method, and get the new ID
        TestUserEntity b = testUserDAO.create(BeanCopyUtil.copy(expectedUserEntity));
        if (Objects.isNull(b)) {
            return ResponseEntity.error("create is null");
        }
        //3.Call the findById(id) method to query the entity object in the database
        Optional<TestUserEntity> actualUserOptional = testUserDAO.findById(b.getId());
        if (actualUserOptional.isEmpty()) {
            return ResponseEntity.error("actualUserOptional is not present");
        }
        TestUserEntity actualUserEntity = actualUserOptional.get(); //调用findById(uuid)

        //4.Assertion: Whether the content of the object detected in step 3 is the same as that of the object created in step 1
        AssertionUtils.assertObjectsEqual(expectedUserEntity, actualUserEntity, Arrays.asList(new String[]{"age", "name", "gender", "birthDate"}));
        assertDefaultFields("create", actualUserEntity);
        // -------- test action end --------------
//        } catch (Exception e) {
//            return ResponseEntity.error(e.getMessage()+e.getCause());
//        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "create(E entity) : E",
            summary = "The length of the inserted field exceeds the field length of the DB table",
            caseNumber = "tc002"
    )
    public ResponseEntity tc002() {
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity = TestUserEntity<String> {
                name: "> is greater than 100 characters"
                age: "> is greater than 3 digits"
                gender: Gender.Male,
                birthDate: ZonedDateTime.now()
            } */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
            expectedUserEntity.setName(RandomUtils.randomString(101, 200));
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());


            //2.Invocation: Cretan (Entity) method
            //3.Catch the call exception of the second step, assert the exception
            AssertionUtils.assertException(() -> testUserDAO.create(expectedUserEntity));
            // -------- test action end --------------
        } catch (Exception e) {

            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "create(E entity) : E",
            summary = "The not null field of the DB table is inserted with a null value",
            caseNumber = "tc003"
    )
    public ResponseEntity tc003() {
        ResponseEntity responseEntity = new ResponseEntity();
        responseEntity.setIsSuccess(false);
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity = TestUserEntity<String> {
                name: null
                age: "Random numbers from 1 to 2"
                gender: Gender.Male,
                birthDate: ZonedDateTime.now()
            } */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
            expectedUserEntity.setName(null);
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());


            //2.Invocation: Cretan (Entity) method
            //3.Catch the call exception of the second step, assert the exception
            AssertionUtils.assertException(() -> testUserDAO.create(expectedUserEntity));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "create(E entity) : E",
            summary = "The ID of the new entity already exists in the database table",
            caseNumber = "tc004"
    )
    public ResponseEntity tc004() {
        try {
            // -------- test action start --------------
            //1.Call findById('create_test_id') to get the pre-prepared data
            Optional<TestUserEntity> expectedUserEntity = testUserDAO.findById("create_test_id"); // findById('create_test_id')
            if (expectedUserEntity.isEmpty()) {
                return ResponseEntity.error("expectedUserEntity is not present");
            }

            //2.Using the results of the first step, call the create method
            //3.Catch the call exception of the second step, assert the exception
            AssertionUtils.assertException(() -> testUserDAO.create(expectedUserEntity.get()));
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "create(E entity, String id)  : E",
            summary = "Create a new entity and specify an ID that does not exist in the database, and the creation is successful",
            caseNumber = "tc005"
    )
    public ResponseEntity tc005() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            /* 1.Create a new entity object entity = TestUserEntity<String> {
                name: "Random 1 - 100 characters"
                age: "Random numbers from 1 to 2"
                gender: Gender.Female,
                birthDate: ZonedDateTime.now()
            } */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
            expectedUserEntity.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());

            //2.call create(entity, UUID)
            TestUserEntity a = testUserDAO.create(BeanCopyUtil.copy(expectedUserEntity), IdWorker.get32UUID());
            if (Objects.isNull(a)) {
                return ResponseEntity.error("create is null");
            }
            //3.Call the findById(id) method to query the entity object in the database
            Optional<TestUserEntity> actualUserOptional = testUserDAO.findById(a.getId());
            if (actualUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity = actualUserOptional.get();

            //4.Assertion: Whether the content of the object detected in step 4 is the same as that of the object created in step 1
            AssertionUtils.assertObjectsEqual(expectedUserEntity, actualUserEntity, Arrays.asList(new String[]{"age", "name", "gender", "birthDate"}));
            assertDefaultFields("create", actualUserEntity);
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "create(E entity, String id)  : E",
            summary = "Create a new entity (id specified as UUID1) and specify another id UUID2 that does not exist in the database in the id parameter",
            caseNumber = "tc006"
    )
    public ResponseEntity tc006() {
        try {
            // -------- test action start --------------
            /* 1.Create a new entity object entity = TestUserEntity<String> {
                name: "Random 1 - 100 characters"
                age: "Random numbers from 1 to 2"
                gender: Gender.Female,
                birthDate: ZonedDateTime.now()
            }
            entity.id = UUID */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setId(IdWorker.get32UUID());
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
            expectedUserEntity.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());

            //2.call create(entity, UUID)

            String createId = IdWorker.get32UUID();
            TestUserEntity a = testUserDAO.create(BeanCopyUtil.copy(expectedUserEntity), createId);
            if (Objects.isNull(a)) {
                return ResponseEntity.error("create is null");
            }
            //3.Call the findById(id) method to query the entity object in the database
            Optional<TestUserEntity> actualUserOptional = testUserDAO.findById(createId);
            if (actualUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity = actualUserOptional.get();

            //4.Assertion: Whether the content of the object detected in step 4 is the same as that of the object created in step 1
            expectedUserEntity.setId(createId);
            AssertionUtils.assertObjectsEqual(expectedUserEntity, actualUserEntity, Arrays.asList(new String[]{"age", "name", "gender", "birthDate"}));
            assertDefaultFields("create", actualUserEntity);
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    @TestCaseAnnotation(
            testSet = "create(E entity, String id)  : E",
            summary = "Create a new entity and specify the id parameter as null, using the new UUID",
            caseNumber = "tc007"
    )
    public ResponseEntity tc007() {
        try {
            // -------- test action start --------------
            /* 1.Create a new entity object entity = TestUserEntity<String> {
                name: "Random 1 - 100 characters"
                age: "Random numbers from 1 to 2"
                gender: Gender.Female,
                birthDate: ZonedDateTime.now()
            } */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setId(null);
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(2));
            expectedUserEntity.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());

            //2.call create(entity, null)
            String createId = IdWorker.get32UUID();
            TestUserEntity a = testUserDAO.create(BeanCopyUtil.copy(expectedUserEntity), createId);
            if (Objects.isNull(a)) {
                return ResponseEntity.error("create is null");
            }

            //3.Call the findById(id) method to query the entity object in the database
            Optional<TestUserEntity> actualUserOptional = testUserDAO.findById(createId);
            if (actualUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity = actualUserOptional.get();

            //4.Assertion: Whether the content of the object detected in step 4 is the same as that of the object created in step 1
            expectedUserEntity.setId(createId);
            //verifyEntity(expectedUserEntity, actualUserEntity);
            assertDefaultFields("create", actualUserEntity);

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "create(E entity, String id)  : E",
            summary = "The ID of the new entity already exists in the database table",
            caseNumber = "tc008"
    )
    public ResponseEntity tc008() {
        try {
            // -------- test action start --------------
            /* 1.Create a new entity objectentity = TestUserEntity<String> {
                name: "Random 1 - 100 characters"
                age: "Random numbers from 1 to 2"
                gender: Gender.Female,
                birthDate: ZonedDateTime.now()
            } */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(2));
            expectedUserEntity.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());


            //2.call create(entity, "create_test_id")
            //3.Catch the call exception of the second step, assert the exception
            AssertionUtils.assertException(() -> testUserDAO.create(BeanCopyUtil.copy(expectedUserEntity), "create_test_id"));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    @TestCaseAnnotation(
            testSet = "create(E entity, String id)  : E",
            summary = "The length of the inserted field exceeds the field length of the DB table",
            caseNumber = "tc009"
    )
    public ResponseEntity tc009() {
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity = TestUserEntity<String> {
                name: "> is greater than 100 characters"
                age: "> is greater than 3 digits"
                gender: Gender.Male,
                birthDate: ZonedDateTime.now()
            } */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
            expectedUserEntity.setName(RandomUtils.randomString(101, 200));
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());


            //2.call: create(entity, UUID)
            //3.Catch the call exception of the second step, assert the exception
            AssertionUtils.assertException(() -> testUserDAO.create(BeanCopyUtil.copy(expectedUserEntity), IdWorker.get32UUID()));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "create(E entity, String id)  : E",
            summary = "The not null field of the DB table is inserted with a null value",
            caseNumber = "tc0010"
    )
    public ResponseEntity tc0010() {
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity = TestUserEntity<String> {
                name: null
                age: "> is greater than 3 digits"
                gender: Gender.Male,
                birthDate: ZonedDateTime.now()
            } */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
            expectedUserEntity.setName(null);
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());

            //2.call: create(entity, UUID)
            //3.Catch the call exception of the second step, assert the exception
            AssertionUtils.assertException(() -> testUserDAO.create(BeanCopyUtil.copy(expectedUserEntity), IdWorker.get32UUID()));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "update(E entity)  : E",
            summary = "Verify that the information for entities that already exist in the database is updated, updating all fields",
            caseNumber = "tc0011"
    )
    public ResponseEntity tc0011() {
        try {
            // -------- test action start --------------
            //1.Call findById('update_test_id') to get the pre-prepared data
            Optional<TestUserEntity> expectedUserOptional = testUserDAO.findById("update_test_id");
            if (expectedUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity expectedUserEntity = expectedUserOptional.get();
            expectedUserEntity.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(2));
            expectedUserEntity.setGender(UserGenderEnum.Female.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());

            //3.Call the update method
            testUserDAO.update(BeanCopyUtil.copyTwo(expectedUserEntity));

            //4.Call the findById method to query the entity object in the database
            Optional<TestUserEntity> actualUserOptional = testUserDAO.findById("update_test_id");
            if (actualUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity = actualUserOptional.get();

            //5.Assertion: Whether the content of the object detected in step 4 is consistent with the content of the object modified in step 2
            AssertionUtils.assertObjectsEqual(expectedUserEntity, actualUserEntity, Arrays.asList(new String[]{"age", "name", "gender", "birthDate"}));

            // -------- test action end --------------

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "update(E entity)  : E",
            summary = "Verify that the information for entities that already exist in the database is updated, and some fields are updated",
            caseNumber = "tc0012"
    )
    public ResponseEntity tc0012() {
        try {
            // -------- test action start --------------
            //1.Call findById('update_test_id') to get the pre-prepared data
            Optional<TestUserEntity> expectedUserOptional = testUserDAO.findById("update_test_id");
            if (expectedUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity expectedUserEntity = expectedUserOptional.get();

            /* 2.Modify the attribute value of the entity object
            name = 'Random 20-bit string' */
            expectedUserEntity.setName(RandomUtils.randomString(1, 100));

            //3.Call the update method
            testUserDAO.update(BeanCopyUtil.copyTwo(expectedUserEntity));

            //4.Call the findById method to query the entity object in the database
            Optional<TestUserEntity> actualUserOptional = testUserDAO.findById("update_test_id");
            if (actualUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity = actualUserOptional.get(); //调用findById(uuid)

            //5.断言: 第4步查出的对象内容是否与第2步修改的对象内容一致
            AssertionUtils.assertObjectsEqual(expectedUserEntity, actualUserEntity, Arrays.asList(new String[]{"age", "name", "gender", "birthDate"}));
            assertDefaultFields("update", actualUserEntity);

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "update(E entity)  : E",
            summary = "Verify and update the information of the entities that already exist in the database, set NULL, and the value is not modified",
            caseNumber = "tc0013"
    )
    public ResponseEntity tc0013() {
        try {
            // -------- test action start --------------
            //1.Call findById('update_test_id') to get the pre-prepared data
            Optional<TestUserEntity> actualUserOptional = testUserDAO.findById("update_test_id");
            if (actualUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity = actualUserOptional.get();
            //2.Modify the attribute value of the entity object age = null & gender = null
            actualUserEntity.setAge(null);
            actualUserEntity.setGender(null);
            //3.Call the update method
            //4.Capture the call of the third step and assert the exception
            testUserDAO.update(BeanCopyUtil.copyTwo(actualUserEntity));

            Optional<TestUserEntity> actualUserOptional2 = testUserDAO.findById("update_test_id");
            if (actualUserOptional2.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity2 = actualUserOptional2.get();
            AssertionUtils.isTrue(Objects.nonNull(actualUserEntity2.getName()), "name should not be null");
            AssertionUtils.isTrue(Objects.nonNull(actualUserEntity2.getAge()), "age should not be null");

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "update(E entity)  : E",
            summary = "The updated field length is greater than the length set by the table field",
            caseNumber = "tc0014"
    )
    public ResponseEntity tc0014() {
        try {
            // -------- test action start --------------
            //1.Call findById('update_test_id') to get the pre-prepared data
            Optional<TestUserEntity> actualUserOptional = testUserDAO.findById("update_test_id");
            if (actualUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity = actualUserOptional.get();

            //2.Modify the attribute value of the entity object name = '> randomly greater than 100 characters',
            actualUserEntity.setName(RandomUtils.randomString(101, 200));
            //3.Call the update method
            //4.Capture the call of the third step and assert the exception
            AssertionUtils.assertException(() -> testUserDAO.update(BeanCopyUtil.copyTwo(actualUserEntity)));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "update(E entity)  : E",
            summary = "Update the data row for an ID that does not exist in the database",
            caseNumber = "tc0015"
    )
    public ResponseEntity tc0015() {
        try {
            // -------- test action start --------------
            /* 1.Create a new entity object TestUserEntity<String> {
                name: "Random 1 - 100 characters"
                age: "Random numbers from 1 to 2"
                gender: Gender.Female,
                birthDate: ZonedDateTime.now()
            }
            entity.id = UUID*/
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setId(IdWorker.get32UUID());
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
            expectedUserEntity.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());

            //2.Call the update method
            //3.Capture the call of the third step and assert the exception
            AssertionUtils.assertException(() -> testUserDAO.update(BeanCopyUtil.copyTwo(expectedUserEntity)));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "update(E entity)  : E",
            summary = "Update the ID of the object to null",
            caseNumber = "tc0016"
    )
    public ResponseEntity tc0016() {
        try {
            // -------- test action start --------------
            //1.Call findById('update_test_id') to get the pre-prepared data
            Optional<TestUserEntity> actualUserOptional = testUserDAO.findById("update_test_id");
            if (actualUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity = actualUserOptional.get();

            //2.Modify the attribute value id = null for the entity object
            actualUserEntity.setId(null);
            //3.Call the update method
            //4.Capture the call of the third step and assert the exception
            AssertionUtils.assertException(() -> testUserDAO.update(BeanCopyUtil.copyTwo(actualUserEntity)));
            // -------- test action end --------------

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "delete(E entity)  : E",
            summary = "Verify that the ID entity already exists in the database is deleted",
            caseNumber = "tc0017"
    )
    public ResponseEntity tc0017() {
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity = TestUserEntity<String> {
                name: "Random 1 - 100 characters"
                age: "Random numbers from 1 to 2"
                gender: UserGenderEnum.Male,
                birthDate: ZonedDateTime.now()
            } */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
            expectedUserEntity.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());


            //2.Invoke: Crete (Entity) method, and get the new ID
            TestUserEntity a = testUserDAO.create(BeanCopyUtil.copy(expectedUserEntity));
            if (Objects.isNull(a)) {
                return ResponseEntity.error("create is null");
            }
            //3.Call findById('new id from step 2') to get the inserted data above
            Optional<TestUserEntity> actualUserOptional = testUserDAO.findById(a.getId());
            if (actualUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity = actualUserOptional.get();
            //4.Call the delete method
            testUserDAO.delete(BeanCopyUtil.copyTwo(actualUserEntity));
            //5.Call the findById('new id from step 2') method
            //6.Assertion: The object detected in step 5 is empty
            if (testUserDAO.findById(a.getId()).isPresent()) {
                return ResponseEntity.error("delete actualUserEntity is not present");
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "delete(E entity)  : E",
            summary = "Verify that the ID entity that does not exist in the database is removed",
            caseNumber = "tc0018"
    )
    public ResponseEntity tc0018() {
        try {
            // -------- test action start --------------
            /* 1.Create a new entity object TestUserEntity<String> {
                name: "Random 1 - 100 characters",
                age: "Random numbers from 1 to 2",
                gender: Gender.Female,
                birthDate: ZonedDateTime.now()
            }
            entity.id = UUID */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setId(IdWorker.get32UUID());
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
            expectedUserEntity.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());


            //2.Call: delete(entity)
            //3.Capture the call of the second step and assert the exception
            AssertionUtils.assertException(() -> testUserDAO.delete(BeanCopyUtil.copyTwo(expectedUserEntity)));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "delete(E entity)  : E",
            summary = "The deleted entity ID is null",
            caseNumber = "tc0019"
    )
    public ResponseEntity tc0019() {
        try {
            // -------- test action start --------------
            /* 1.Create a new entity object TestUserEntity<String> {
                name: "Random 1 - 100 characters",
                age: "Random numbers from 1 to 2",
                gender: Gender.Female,
                birthDate: ZonedDateTime.now()
            }
            entity.id =  null */
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setId(null);
            expectedUserEntity.setAge(RandomUtils.randomNDigitInt(3));
            expectedUserEntity.setName(RandomUtils.randomString(1, 100));
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(ZonedDateTime.now());

            //2.Call: delete(entity)
            //3.Capture the call of the second step and assert the exception
            AssertionUtils.assertException(() -> testUserDAO.delete(BeanCopyUtil.copyTwo(expectedUserEntity)));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findById(String id)  : E",
            summary = "Query for ID that already exist in the database",
            caseNumber = "tc0020"
    )
    public ResponseEntity tc0020() {
        try {
            // -------- test action start --------------
            //1.Call findById('find_test_id') to get the pre-prepared data
            TestUserEntity expectedUserEntity = new TestUserEntity();
            expectedUserEntity.setId("find_test_id");
            expectedUserEntity.setAge(11);
            expectedUserEntity.setName("find_test_name");
            expectedUserEntity.setGender(UserGenderEnum.Male.name());
            expectedUserEntity.setBirthDate(convertStringToZonedDateTime("2024-06-25 17:50:33"));

            Optional<TestUserEntity> actualUserOptional = testUserDAO.findById("find_test_id");
            if (actualUserOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestUserEntity actualUserEntity = actualUserOptional.get();
            //2.Assertion: The content of step 1 is consistent with the pre-prepared data
            AssertionUtils.assertObjectsEqual(expectedUserEntity, actualUserEntity, Arrays.asList(new String[]{"age", "name", "gender", "birthDate"}));
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findById(String id)  : E",
            summary = "Querying ids that do not exist in the database returns an empty Optional",
            caseNumber = "tc0021"
    )
    public ResponseEntity tc0021() {
        try {
            // -------- test action start --------------
            //1.Call findById(UUID) (database does not exist)
            //2.Assertion: The object detected in step 1 is empty isPresent()
            Optional<TestUserEntity> expectedUserOptional = testUserDAO.findById(IdWorker.get32UUID());
            if (expectedUserOptional.isPresent()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findAll(int pageNumber, int pageSize, SortCriteria sortCriteria) : PageResult< :E>",
            summary = "Search and return the entity and quantity fields of a page according to the specified [pageNumber+pageSize], without specifying the sort",
            caseNumber = "tc0022"
    )
    public ResponseEntity tc0022() {
        try {
            // -------- test action start --------------
            //1.call findAll(2, 10, null)
            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("index_num_1", "asc");
            PageResult<TestFindAllEntity> expected = testFindAllDAO.findAll(2, 10, sortCriteria);
            //2.Assert whether the results found are correct
            assertPageResult(expected, 1001, 101, 2, 10, true);
            for (int i = 0; i < 10; i++) {
                Assert.isTrue(expected.getData().get(i).getIndexNum1() == (i + 11), "Values do not match for field: indexNum1");
                Assert.isTrue(expected.getData().get(i).getIndexNum2() == 2, "Values do not match for field: indexNum2");
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    @TestCaseAnnotation(
            testSet = "findAll(int pageNumber, int pageSize, SortCriteria sortCriteria) : PageResult< :E>",
            summary = "Search for and return the entity and quantity fields of a page in the middle according to the specified pageNumber+pageSize, and sort indexNum2 in reverse order + indexNum1 in positive order",
            caseNumber = "tc0023"
    )
    public ResponseEntity tc0023() { // TODO: 变动
        try {
            // -------- test action start --------------
            //1.call findAll(100, 10, indexNum2:desc, indexNum1:asc;)
            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("index_num_2", "desc");
            sortCriteria.getCriteria().put("index_num_1", "asc");
            PageResult<TestFindAllEntity> expected = testFindAllDAO.findAll(100, 10, sortCriteria);
            //2.Assert whether the results found are correct

            //2.Assert whether the results found are correct
            assertPageResult(expected, 1001, 101, 100, 10, true);
            Assert.isTrue(expected.getData().get(0).getIndexNum1() == 20, "Values do not match for field: indexNum1");
            Assert.isTrue(expected.getData().get(0).getIndexNum2() == 2, "Values do not match for field: indexNum2");

            for (int i = 1; i < 9; i++) {
                Assert.isTrue(expected.getData().get(i).getIndexNum1() == i, "Values do not match for field: indexNum1");
                Assert.isTrue(expected.getData().get(i).getIndexNum2() == 1, "Values do not match for field: indexNum2");
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findAll(int pageNumber, int pageSize, SortCriteria sortCriteria) : PageResult< :E>",
            summary = "Search for the data on the last page of the data table according to the specified pageNumber+pageSize, and return the entity and quantity fields on the last page, indexNum1 is arranged",
            caseNumber = "tc0024"
    )
    public ResponseEntity tc0024() {
        try {
            // -------- test action start --------------
            //1.call findAll(101, 10, indexNum1:asc)
            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("index_num_1", "asc");
            PageResult<TestFindAllEntity> expected = testFindAllDAO.findAll(101, 10, sortCriteria);
            //2.Assert whether the results found are correct
            assertPageResult(expected, 1001, 101, 101, 10, false);
            if (!expected.getData().isEmpty() && expected.getData().size() == 1) {
                Assert.isTrue(expected.getData().get(0).getIndexNum1() == 1001, "Values do not match for field: indexNum1");
                Assert.isTrue(expected.getData().get(0).getIndexNum2() == 101, "Values do not match for field: indexNum2");
            } else {
                return ResponseEntity.error("data size is not 1");
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findAll(int pageNumber, int pageSize, SortCriteria sortCriteria) : PageResult< :E>",
            summary = "The pageNumberpageSize of the query exceeds the amount of data in the table",
            caseNumber = "tc0025"
    )
    public ResponseEntity tc0025() {
        try {
            // -------- test action start --------------
            //1.call findAll(99999,10, null)
            PageResult<TestFindAllEntity> expected = testFindAllDAO.findAll(99999, 10, null);
            //2.Assert whether the results found are correct
            assertPageResult(expected, 1001, 101, 99999, 10, false);
            if (!expected.getData().isEmpty()) {
                return ResponseEntity.error("data is not isEmpty");
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findAll(int pageNumber, int pageSize, SortCriteria sortCriteria) : PageResult< :E>",
            summary = "Use negative numbers pageNumber",
            caseNumber = "tc0026"
    )
    public ResponseEntity tc0026() {
        try {
            // -------- test action start --------------
            //1. call findAll(-11, 10, null)
            //2. Capture the call of the first step, asserting the exception
            AssertionUtils.assertException(() -> testFindAllDAO.findAll(-11, 10, null));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findAll(int pageNumber, int pageSize, SortCriteria sortCriteria) : PageResult< :E>",
            summary = "Use negative numbers pageSize",
            caseNumber = "tc0027"
    )
    public ResponseEntity tc0027() {
        try {
            // -------- test action start --------------
            //1. call findAll(1, -10, null)
            //2. Capture the call of the first step, asserting the exception
            AssertionUtils.assertException(() -> testFindAllDAO.findAll(1, -10, null));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findAll(int pageNumber, int pageSize, SortCriteria sortCriteria) : PageResult< :E>",
            summary = "Use 0 as pageNumber",
            caseNumber = "tc0028"
    )
    public ResponseEntity tc0028() {
        try {
            // -------- test action start --------------
            //1. call findAll(0,10, null)
            //2. Capture the call of the first step, asserting the exception
            AssertionUtils.assertException(() -> testFindAllDAO.findAll(0, 10, null));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findAll(int pageNumber, int pageSize, SortCriteria sortCriteria) : PageResult< :E>",
            summary = "Use 0 as pageSize",
            caseNumber = "tc0029"
    )
    public ResponseEntity tc0029() {
        try {
            // -------- test action start --------------
            //1. call findAll(1,0, null)
            //2. Capture the call of the first step, asserting the exception
            AssertionUtils.assertException(() -> testFindAllDAO.findAll(1, 0, null));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findAll(int pageNumber, int pageSize, SortCriteria sortCriteria) : PageResult< :E>",
            summary = "Specifies a field sort query that does not exist",
            caseNumber = "tc0030"
    )
    public ResponseEntity tc0030() {
        try {
            // -------- test action start --------------
            //1.call findAll(1, 10, error:desc) Fields that don't exist
            //2.Capture the call of the first step, asserting the exception
            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("error", "desc");
            AssertionUtils.assertException(() -> testFindAllDAO.findAll(1, 10, sortCriteria));
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "JDBCDataSourceConfig",
            summary = "Verify the default configuration connection information of the database",
            caseNumber = "tc0031"
    )
    public ResponseEntity tc0031() {
        try {
            // -------- test action start --------------
            //1.Demo Introduction SDK Launch Project Configure password url username
            /* 2.Assertion: The following default configuration of the SDK is obtained through reflection and the value is correct
            minimumIdle/maximumPoolSize/idleTimeout/connectionTimeout/maxLifetime/leakDetectionThreshold */
            AssertionUtils.isTrue(Objects.equals(hikariDataSource.getMinimumIdle(), 10), "MinimumIdle is not equal");
            AssertionUtils.isTrue(Objects.equals(hikariDataSource.getMaximumPoolSize(), 10), "MaximumPoolSize is not equal");
            AssertionUtils.isTrue(Objects.equals(hikariDataSource.getConnectionTimeout(), 30000L), "ConnectionTimeout is not equal");
            AssertionUtils.isTrue(Objects.equals(hikariDataSource.getLeakDetectionThreshold(), 0L), "LeakDetectionThreshold is not equal");
            AssertionUtils.isTrue(Objects.equals(hikariDataSource.getMaxLifetime(), 1800000L), "MaxLifetime is not equal");
            AssertionUtils.isTrue(Objects.equals(hikariDataSource.getIdleTimeout(), 600000L), "IdleTimeout is not equal");

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    @TestCaseAnnotation(
            testSet = "create(E entity) : E",
            summary = "(Auto-increment ID) creates a new entity data as normal",
            caseNumber = "tc0032"
    )
    public ResponseEntity tc0032() {
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity= TestOrderEntity<Long>  {
                price: "20.22"
            } */
            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("id", "desc");
            PageResult<TestOrderEntity> orderEntityPageResult = testOrderDAO.findAll(1, 1, sortCriteria);
            if (orderEntityPageResult.getData().isEmpty()) {
                return ResponseEntity.error("orderEntityPageResult.getData().isEmpty()");
            }
            Integer lastID = orderEntityPageResult.getData().getFirst().getId();

            TestOrderEntity expectedOrderEntity = new TestOrderEntity();
            expectedOrderEntity.setPrice(String.valueOf(RandomUtils.randomNDigitInt(2)));
            expectedOrderEntity.setDeleted(false);

            //2.Invocation: Cretan (Entity) method
            TestOrderEntity b = testOrderDAO.create(expectedOrderEntity);
            if (Objects.isNull(b)) {
                return ResponseEntity.error("create is null");
            }
            //3.Call the findById(id) method to query the entity object in the database
            Optional<TestOrderEntity> actualOrderOptional = testOrderDAO.findById(b.getId());
            if (actualOrderOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestOrderEntity actualOrderEntity = actualOrderOptional.get();

            //4.Assertion: Whether the content of the object detected in step 4 is the same as that of the object created in step 1
            AssertionUtils.isTrue(actualOrderEntity.getId().equals(lastID + 1), "The ID is not self-incrementing");
            AssertionUtils.assertObjectsEqual(expectedOrderEntity, actualOrderEntity, Arrays.asList(new String[]{"deleted", "price"}));
            assertDefaultFields("create", actualOrderEntity);
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "create(E entity, Long id)  : E",
            summary = " (Auto-Increment ID) creates a new entity and specifies an ID that does not exist in the database",
            caseNumber = "tc0033"
    )
    public ResponseEntity tc0033() {
        try {
            // -------- test action start --------------
            //1.lastID = findAll(1, 1, id:desc) Get the ID of the last row
            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("id", "desc");
            PageResult<TestOrderEntity> orderEntityPageResult = testOrderDAO.findAll(1, 1, sortCriteria);
            if (orderEntityPageResult.getData().isEmpty()) {
                return ResponseEntity.error("orderEntityPageResult.getData().isEmpty()");
            }
            //2.call create(entity, lastID+10) method and get the latest ID
            TestOrderEntity expectedOrderEntity = new TestOrderEntity();
            expectedOrderEntity.setPrice(String.valueOf(RandomUtils.randomNDigitInt(3)));

            Integer lastID = orderEntityPageResult.getData().getFirst().getId();
            Integer newId = lastID + 100;
            TestOrderEntity b = testOrderDAO.create(expectedOrderEntity, newId);
            if (Objects.isNull(b)) {
                return ResponseEntity.error("create is null");
            }
            //3.call findById(ID)
            //4.Assertion: Whether the content of the object detected in step 3 is the same as that of the object created in step 2
            Optional<TestOrderEntity> actualOrderOptional = testOrderDAO.findById(newId);
            if (actualOrderOptional.isEmpty()) {
                return ResponseEntity.error("actualOrderOptional is null");
            }

            /* 5.Create: A new entity object entity= TestOrderEntity<Long>  {
                price: "10.22"
            } */
            TestOrderEntity expectedOrderEntity2 = new TestOrderEntity();
            expectedOrderEntity2.setPrice(String.valueOf(RandomUtils.randomNDigitInt(2)));
            //7.Invocation: Cretan (Entity) method
            TestOrderEntity c = testOrderDAO.create(expectedOrderEntity2);
            if (Objects.isNull(c)) {
                return ResponseEntity.error("create is null");
            }
            //9.Call the findById(id) method to query the entity object in the database
            Optional<TestOrderEntity> actualOrderOptional2 = testOrderDAO.findById(c.getId());
            if (actualOrderOptional2.isEmpty()) {
                return ResponseEntity.error("actualOrderOptional2 is null");
            }
            testOrderDAO.deleteId(newId);
            //10.Assertion: Whether the content of the object detected in step 8 is consistent with the content of the object created in step 7
            AssertionUtils.assertObjectsEqual(expectedOrderEntity2, actualOrderOptional2.get(), Arrays.asList(new String[]{"price"}));
            //11.Assertion: ID and designation of the use case above lastID+10 + 1
            AssertionUtils.isTrue(actualOrderOptional2.get().getId().equals(newId + 1), "ID is not equal");
            assertDefaultFields("create", actualOrderOptional2.get());

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    @TestCaseAnnotation(
            testSet = "(Auto-increment ID) update(E entity)  : E",
            summary = "Verify the update of information for entities that already exist in the database",
            caseNumber = "tc0034"
    )
    public ResponseEntity tc0034() {
        try {
            // -------- test action start --------------
            //1.call findById(2) Get pre-prepared data
            Optional<TestOrderEntity> expectedOrderOptional = testOrderDAO.findById(2);
            if (expectedOrderOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestOrderEntity expectedOrderEntity = expectedOrderOptional.get();
            /* 2.Modify the attribute value of the entity object price = Random 2 decimal places */
            expectedOrderEntity.setPrice(String.valueOf(RandomUtils.randomNDigitInt(3)));

            //3.Call the update method
            testOrderDAO.update(BeanCopyUtil.copyTwo(expectedOrderEntity));

            //4.call findById(2)
            Optional<TestOrderEntity> actualOrderOptional = testOrderDAO.findById(2);
            if (actualOrderOptional.isEmpty()) {
                return ResponseEntity.error("actualOrderOptional is not present");
            }
            TestOrderEntity actualOrderEntity = actualOrderOptional.get();

            //5.Assertion: Whether the content of the object detected in step 4 is consistent with the content of the object modified in step
            AssertionUtils.assertObjectsEqual(expectedOrderEntity, actualOrderEntity, Arrays.asList(new String[]{"price"}));
            assertDefaultFields("update", actualOrderEntity);

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    @TestCaseAnnotation(
            testSet = "(Auto-increment ID) delete(E entity)  : E",
            summary = "Verify that the ID entity already exists in the database is deleted",
            caseNumber = "tc0035"
    )
    public ResponseEntity tc0035() {
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity= TestOrderEntity<Long>  {
                price: "10.22"
            } */
            TestOrderEntity expectedOrderEntity = new TestOrderEntity();
            expectedOrderEntity.setPrice(String.valueOf(RandomUtils.randomNDigitInt(3)));

            //2.Invoke: Crete (Entity) method, and get the new ID
            TestOrderEntity a = testOrderDAO.create(BeanCopyUtil.copy(expectedOrderEntity));
            if (Objects.isNull(a)) {
                return ResponseEntity.error("create is null");
            }
            //3.Call findById('new id from step 2')
            Optional<TestOrderEntity> actualOrderOptional = testOrderDAO.findById(a.getId());
            if (actualOrderOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestOrderEntity actualOrderEntity = actualOrderOptional.get();
            //4.Call the delete method
            testOrderDAO.delete(BeanCopyUtil.copyTwo(actualOrderEntity));
            //5.Call findById('new id from step 2') method
            //6.Assertion: The object detected in step 5 is empty
            if (testOrderDAO.findById(a.getId()).isPresent()) {
                return ResponseEntity.error("delete actualUserEntity is not present");
            }

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "(Auto-increment ID) findById(Long id)  : E",
            summary = "Query for ID that already exist in the database",
            caseNumber = "tc0036"
    )
    public ResponseEntity tc0036() {
        try {
            // -------- test action start --------------
            //1.call findById(1) Get pre-prepared data
            TestOrderEntity expectedOrderEntity = new TestOrderEntity();
            expectedOrderEntity.setId(1);
            expectedOrderEntity.setPrice("11.22");

            Optional<TestOrderEntity> actualOrderOptional = testOrderDAO.findById(1);
            if (actualOrderOptional.isEmpty()) {
                return ResponseEntity.error("actualOrderOptional is not present");
            }
            TestOrderEntity actualOrderEntity = actualOrderOptional.get();
            //2.Assertion: The content of step 1 is consistent with the pre-prepared data
            AssertionUtils.assertObjectsEqual(expectedOrderEntity, actualOrderEntity, Arrays.asList(new String[]{"id", "price"}));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    @TestCaseAnnotation(
            testSet = "(Auto-increment ID) create(E entity, Long id): E",
            summary = "Create an existing ID that has been tombstoned",
            caseNumber = "tc0037"
    )
    public ResponseEntity tc0037() {
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity= TestOrderEntity<Long>  {
                price: "20.22"
            } */
            TestOrderEntity expectedOrderEntity = new TestOrderEntity();
            expectedOrderEntity.setId(3);
            expectedOrderEntity.setPrice("10.22");

            //2.Call: create(entity, 3)
            AssertionUtils.assertException(() -> testOrderDAO.create(expectedOrderEntity, 3));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "(Auto-increment ID) findById(Long id)  : E",
            summary = "The query has been tombstoned with an ID and the query result is empty",
            caseNumber = "tc0038"
    )
    public ResponseEntity tc0038() {
        try {
            // -------- test action start --------------
            //1.Call findById(3)
            Optional<TestOrderEntity> testOrderEntity = testOrderDAO.findById(3);
            //2.Assertion: The object detected in step 1 is empty
            if (testOrderEntity.isPresent()) {
                return ResponseEntity.error("testOrderEntity is not present");
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findAll(int pageNumber, int pageSize, SortCriteria sortCriteria)",
            summary = "The query is tombstoned, and the query result is empty",
            caseNumber = "tc0039"
    )
    public ResponseEntity tc0039() {
        try {
            // -------- test action start --------------
            //1. Call findAll(1, 10, null)
            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("id", "asc");
            PageResult<TestOrderEntity> testOrderEntityPageResult = testOrderDAO.findAll(1, 10, sortCriteria);

            //2. The assertion content does not contain data with ID = 3
            boolean exists = testOrderEntityPageResult.getData().stream().anyMatch(entity -> Objects.equals(entity.getId(), 3));
            AssertionUtils.isTrue(!exists, "ID = 3 is exist");
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "update(E entity)  : E",
            summary = "Update the deleted field to tombstonestone , which cannot be modified",
            caseNumber = "tc0040"
    )
    public ResponseEntity tc0040() { // tc0040
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity= TestUserEntity<String> {
                name: "Random 1 - 100 characters",
                age: "Random numbers from 1 to 2",
                gender: UserGenderEnum.Male,
                birthDate: ZonedDateTime.now()
            } */
            TestOrderEntity expectedOrderEntity = new TestOrderEntity();
            expectedOrderEntity.setPrice(String.valueOf(RandomUtils.randomNDigitInt(3)));

            //2.Invoke: Crete (Entity) method, and get the new ID
            TestOrderEntity orderEntity = testOrderDAO.create(BeanCopyUtil.copy(expectedOrderEntity));
            if (Objects.isNull(orderEntity)) {
                return ResponseEntity.error("create is null");
            }
            //3.Modify the attribute value of the entity object deleted = 1
            orderEntity.setDeleted(true);

            //4.Call the update method
            testOrderDAO.update(BeanCopyUtil.copyTwo(orderEntity));

            //5.Call the findById method to query the entity object in the database
            Optional<TestOrderEntity> actualOrderOptional = testOrderDAO.findById(orderEntity.getId());
            if (actualOrderOptional.isEmpty()) {
                return ResponseEntity.error("actualOrderOptional is empty");
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "update(E entity)  : E",
            summary = "Update information about entities in the database that have been tombstoned",
            caseNumber = "tc0041"
    )
    public ResponseEntity tc0041() {
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity= TestOrderEntity<Long>  {
                id: 3,
                price: "1111.22"
            } */
            TestOrderEntity expectedOrderEntity = new TestOrderEntity();
            expectedOrderEntity.setPrice("11111.22");
            expectedOrderEntity.setId(3);

            //2.Call the update method
            AssertionUtils.assertException(() -> testOrderDAO.update(BeanCopyUtil.copyTwo(expectedOrderEntity)));
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findPage(int pageNumber, int pageSize, SortCriteria sortCriteria, Wrapper<T> queryWrapper)",
            summary = "Specify an empty queryWrapper filter",
            caseNumber = "tc0042"
    )
    public ResponseEntity tc0042() {
        try {
            // -------- test action start --------------
            //1.Call findPage(1, 10, null, null)
            PageResult<TestFindPageEntity> expected = testFindPageDAO.findPage(1, 10, null, null);

            //2.Assert whether the results found are correct
            assertPageResult(expected, 21, 3, 1, 10, true);
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findPage(int pageNumber, int pageSize, SortCriteria sortCriteria, Wrapper<T> queryWrapper)",
            summary = "Specify a queryWrapper single condition to filter out the specified conditions",
            caseNumber = "tc0043"
    )
    public ResponseEntity tc0043() {
        try {
            // -------- test action start --------------
            //1.Call findPage(3, 10, null, "name: test")
            QueryWrapper<TestFindPageEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", "test");
            PageResult<TestFindPageEntity> expected = testFindPageDAO.findPage(3, 10, null, queryWrapper);

            //2.Assert whether the results found are correct
            assertPageResult(expected, 21, 3, 3, 10, false);
            Assert.isTrue(!expected.getData().isEmpty(), "testFindPageEntity data is empty");
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findPage(int pageNumber, int pageSize, SortCriteria sortCriteria, Wrapper<T> queryWrapper)",
            summary = "Specify queryWrapper multiple conditions to filter out the specified conditions",
            caseNumber = "tc0044"
    )
    public ResponseEntity tc0044() {
        try {
            // -------- test action start --------------
            //1.Call findPage(2, 10, null, "name: test, time: 2024-6-1 to 2024-6-30")
            LambdaQueryWrapper<TestFindPageEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TestFindPageEntity::getName, "test");
            queryWrapper.between(
                    TestFindPageEntity::getTime,
                    convertStringToZonedDateTime("2024-06-01 00:01:01"),
                    convertStringToZonedDateTime("2024-06-30 23:59:59")
            );

            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("index_num_1", "asc");
            PageResult<TestFindPageEntity> expected = testFindPageDAO.findPage(2, 10, sortCriteria, queryWrapper);

            //2.Assert whether the results found are correct
            assertPageResult(expected, 20, 2, 2, 10, false);
            int indexNum = 11;
            for (int i = 0; i < 10; i++) {
                Assert.isTrue(expected.getData().get(i).getIndexNum1() == indexNum, "Values do not match for field: indexNum1");
                indexNum++;
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findPage(int pageNumber, int pageSize, SortCriteria sortCriteria, Wrapper<T> queryWrapper)",
            summary = "Specify queryWrapper condition + sortCriteria to sort out the specified criteria",
            caseNumber = "tc0045"
    )
    public ResponseEntity tc0045() {
        try {
            // -------- test action start --------------
            //1.Call findPage(1, 10, "indexNum1:desc", "name: test")
            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("index_num_1", "desc");

            QueryWrapper<TestFindPageEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", "test");
            PageResult<TestFindPageEntity> expected = testFindPageDAO.findPage(1, 10, sortCriteria, queryWrapper);

            //2.Assert whether the results found are correct
            assertPageResult(expected, 21, 3, 1, 10, true);
            int indexNum = 21;
            for (int i = 0; i < 10; i++) {
                Assert.isTrue(expected.getData().get(i).getIndexNum1() == indexNum, "Values do not match for field: indexNum1");
                indexNum--;
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findPage(int pageNumber, int pageSize, SortCriteria sortCriteria, Wrapper<T> queryWrapper)",
            summary = "Specify a filter condition that does not exist for queryWrapper",
            caseNumber = "tc0046"
    )
    public ResponseEntity tc0046() {
        try {
            // -------- test action start --------------
            //1.Call findPage(1, 10, null, "error: test")
            QueryWrapper<TestFindPageEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("error", "test");

            //2.Assert whether the results found are correct
            AssertionUtils.assertException(() -> testFindPageDAO.findPage(1, 10, null, queryWrapper));
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    @TestCaseAnnotation(
            testSet = "findPage(int pageNumber, int pageSize, SortCriteria sortCriteria, Wrapper<T> queryWrapper)",
            summary = "Filtering content that has been tombstoned and the query result data is empty",
            caseNumber = "tc0047"
    )
    public ResponseEntity tc0047() {
        try {
            // -------- test action start --------------
            /* 1.Create: A new entity object entity= TestFindPageEntity<Long>  {
                name: "test111",
                indexNum1: 111
            } */
            TestFindPageEntity testFindPageEntity = new TestFindPageEntity();
            testFindPageEntity.setName("test111");
            testFindPageEntity.setTime(ZonedDateTime.now());
            testFindPageEntity.setIndexNum1(111);

            //2.Call: create(entity)
            testFindPageDAO.create(testFindPageEntity);

            //3.Call findById('The new ID for step 2')
            Optional<TestFindPageEntity> testFindPageEntityOptional = testFindPageDAO.findById(testFindPageEntity.getId());
            if (testFindPageEntityOptional.isEmpty()) {
                return ResponseEntity.error("testFindPageEntityOptional is not present");
            }
            //4.Call delete()
            testFindPageDAO.delete(testFindPageEntityOptional.get());

            //5.Call findPage(1, 10, "index_num_1:asc", null)
            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("index_num_1", "asc");
            PageResult<TestFindPageEntity> expected = testFindPageDAO.findPage(1, 10, sortCriteria, null);
            //6.Assert whether the results found are correct
            assertPageResult(expected, 21, 3, 1, 10, true);
            for (int i = 0; i < 10; i++) {
                Assert.isTrue(expected.getData().get(i).getIndexNum1() == i + 1, "Values do not match for field: indexNum1");
            }
            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    @TestCaseAnnotation(
            testSet = "update(E entity)  : E",
            summary = "Verify that the optimistic lock is modified multiple times, and an error is reported",
            caseNumber = "tc0048"
    )
    public ResponseEntity tc0048() {
        try {
            // -------- test action start --------------
            //1.call findById(2) Get pre-prepared data
            Optional<TestOrderEntity> expectedOrderOptional = testOrderDAO.findById(2);
            if (expectedOrderOptional.isEmpty()) {
                return ResponseEntity.error("actualUserOptional is not present");
            }
            TestOrderEntity expectedOrderEntity = expectedOrderOptional.get();
            /* 2.Modify the attribute value of the entity object price = Random 2 decimal places */
            expectedOrderEntity.setPrice(String.valueOf(RandomUtils.randomNDigitInt(3)));

            Long oldVersion = expectedOrderEntity.getVersion();
            //3.Call the update method
            testOrderDAO.update(BeanCopyUtil.copyTwo(expectedOrderEntity));

            expectedOrderEntity.setVersion(oldVersion);
            //4.Call the update method
            AssertionUtils.assertException(() -> testOrderDAO.update(BeanCopyUtil.copyTwo(expectedOrderEntity)));

            // -------- test action end --------------
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_1() {
        TestUserEntity entity = new TestUserEntity();
        entity.setId(RandomUtils.randomString(1, 15));
        entity.setAge(RandomUtils.randomNDigitInt(2));
        entity.setName(RandomUtils.randomString(1, 100));
        entity.setGender(UserGenderEnum.Male.name());
        entity.setBirthDate(ZonedDateTime.now());
        List<TestUserEntity> list = new ArrayList<>();
        list.add(entity);
        int result = testUserDAO.createBatch(list);
        Assert.isTrue(result == 1, "result should be 1");
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_2() {
        List<TestUserEntity> list = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            TestUserEntity entity = new TestUserEntity();
            entity.setId(RandomUtils.randomString(1, 15));
            entity.setAge(RandomUtils.randomNDigitInt(2));
            entity.setName(RandomUtils.randomString(1, 100));
            entity.setGender(UserGenderEnum.Male.name());
            entity.setBirthDate(ZonedDateTime.now());
            list.add(entity);
        }
        int result = testUserDAO.createBatch(list);
        Assert.isTrue(result == 3, "result should be 3");
        return ResponseEntity.success();
    }


    public ResponseEntity tc1_3() {
        try {
            int result = testUserDAO.createBatch(new ArrayList<>());
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc1_4() {
        try {
            testUserDAO.createBatch(null);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc1_5() {
        List<TestUserEntity> list = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            TestUserEntity entity = new TestUserEntity();
            entity.setId(RandomUtils.randomString(1, 15));
            entity.setAge(RandomUtils.randomNDigitInt(2));
            entity.setName(RandomUtils.randomString(1, 100));
            entity.setGender(UserGenderEnum.Male.name());
            entity.setBirthDate(ZonedDateTime.now());
            entity.setTenantId(DEV);
            list.add(entity);
        }
        int result = testUserDAO.createBatch(list);
        Assert.isTrue(result == 3, "result should be 3");
        return ResponseEntity.success();
    }




    public static void assertPageResult(
            PageResult ac,
            long totalCount,
            long totalPages,
            int pageNumber,
            int pageSize,
            Boolean hasMore
    ) {

        AssertionUtils.isTrue(ac.getTotalCount() == totalCount, "Total count does not match");
        AssertionUtils.isTrue(ac.getTotalPages() == totalPages, "Total pages does not match");
        AssertionUtils.isTrue(ac.getPageNumber() == pageNumber, "Total pageNumber does not match");
        AssertionUtils.isTrue(ac.getPageSize() == pageSize, "Total pageSize does not match");
        AssertionUtils.isTrue(ac.getHasMore() == hasMore, "hasMore does not match");

        if (ac.getHasMore()) {
            AssertionUtils.isTrue(ac.getData().size() == pageSize, "getData pageSize count does not match");
        }
    }

}