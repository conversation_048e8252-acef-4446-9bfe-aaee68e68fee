package com.kering.cus.qa.service;

import com.kering.cus.lib.common.context.GenericRequestContextHolder;
import com.kering.cus.lib.storage.StorageService;
import com.kering.cus.lib.storage.exception.StorageException;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.utils.FileUtils;
import com.kering.cus.qa.utils.ThrowableUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.*;

import static com.kering.cus.qa.constants.FileStorageSdkConstants.*;
import static com.kering.cus.qa.constants.MessageConstants.*;

@Service
@Slf4j
public class FileStorageSdkTestService {

    @Autowired
    private StorageService storageService;


    public ResponseEntity test001() {
        String path = storageService.writeString(CONTEXT, LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(0));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test002() {
        String path = storageService.writeString(CONTEXT, LOCAL_FILE_DESTINATION_LIST.get(1), PATH_LIST.get(1));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test003() {
        String path = storageService.writeString(CONTEXT, LOCAL_FILE_DESTINATION_LIST.get(1), PATH_LIST.get(2));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test004() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        String path = storageService.writeStream(inputStream, LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(0));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test005() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_EMPTY);
        String path = storageService.writeStream(inputStream, LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(3));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity test006() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        String path = storageService.writeStream(inputStream, LOCAL_FILE_DESTINATION_LIST.get(1), PATH_LIST.get(1));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test007() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        String path = storageService.writeStream(inputStream, LOCAL_FILE_DESTINATION_LIST.get(1), PATH_LIST.get(2));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity test008() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        String path = storageService.writeBytes(bytes, LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(0));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test009() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_EMPTY);
        String path = storageService.writeBytes(bytes, LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(3));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test010() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        String path = storageService.writeBytes(bytes, LOCAL_FILE_DESTINATION_LIST.get(1), PATH_LIST.get(1));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test011() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        String path = storageService.writeBytes(bytes, LOCAL_FILE_DESTINATION_LIST.get(1), PATH_LIST.get(2));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test012() {
        String result = storageService.readString(LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(0));
        Assert.isTrue(null != result, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity test013() throws IOException {
        InputStream result = storageService.readStream(LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(0));
        Assert.isTrue(result.available() > 0, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test014() throws IOException {
        InputStream result = storageService.readStream(LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(3));
        Assert.isTrue(result.available() == 0, EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity test015() {
        byte[] result = storageService.readBytes(LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(0));
        Assert.isTrue(result != null && result.length != 0, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test016() {
        byte[] result = storageService.readBytes(LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(3));
        Assert.isTrue(result.length == 0, EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test017() {
        boolean result = storageService.exists(LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(0));
        Assert.isTrue(result, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test018() {
        boolean result = storageService.exists(LOCAL_FILE_DESTINATION_LIST.get(2), PATH_LIST.get(1));
        Assert.isTrue(!result, EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity test019() {
        boolean result = storageService.exists(LOCAL_FILE_DESTINATION_LIST.get(0), PATH_LIST.get(4));
        Assert.isTrue(!result, EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity tc020() {
        String path = storageService.writeString(CONTEXT, DESTINATION, OSS_LIST.get(0));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }

    public ResponseEntity tc021() {
        String path = storageService.writeString(CONTEXT, DESTINATION, OSS_LIST.get(2));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc022() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        String path = storageService.writeStream(inputStream, DESTINATION, OSS_LIST.get(0));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc023() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_EMPTY);
        String path = storageService.writeStream(inputStream, DESTINATION, OSS_LIST.get(3));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc024() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        String path = storageService.writeStream(inputStream, DESTINATION, OSS_LIST.get(2));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity tc025() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        String path = storageService.writeBytes(bytes, DESTINATION, OSS_LIST.get(0));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc026() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_EMPTY);
        String path = storageService.writeBytes(bytes, DESTINATION, OSS_LIST.get(3));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc027() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        String path = storageService.writeBytes(bytes, DESTINATION, OSS_LIST.get(2));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc028() {
        String path = storageService.readString(DESTINATION, OSS_LIST.get(0));
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity tc029() throws IOException {
        InputStream result = storageService.readStream(DESTINATION, OSS_LIST.get(0));
        Assert.isTrue(result.available() > 0, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc030() throws IOException {
        InputStream result = storageService.readStream(DESTINATION, OSS_LIST.get(3));
        Assert.isTrue(result.available() == 0, EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity tc031() {
        byte[] result = storageService.readBytes(DESTINATION, OSS_LIST.get(0));
        Assert.isTrue(result != null && result.length != 0, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc032() {
        byte[] result = storageService.readBytes(DESTINATION, OSS_LIST.get(3));
        Assert.isTrue(result.length == 0, EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc033() {
        boolean result = storageService.exists(DESTINATION, OSS_LIST.get(0));
        Assert.isTrue(result, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc034() {
        boolean result = storageService.exists(DESTINATION, OSS_LIST.get(5));
        Assert.isTrue(!result, EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity test035() {
        try {
            storageService.writeString(CONTEXT, LOCAL_FILE_DESTINATION_LIST.get(3), PATH_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc036() {
        try {
            storageService.writeString(CONTEXT, NO_DESTINATION, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }


    public ResponseEntity tc037() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        try {
            storageService.writeStream(inputStream, NO_DESTINATION, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }


    public ResponseEntity tc038() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        try {
            storageService.writeBytes(bytes, NO_DESTINATION, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc039() {
        try {
            storageService.readString(NO_DESTINATION, OSS_LIST.get(0));
        } catch (StorageException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc040() {
        try {
            storageService.readString(DESTINATION, OSS_LIST.get(5));
        } catch (com.kering.cus.lib.storage.exception.FileNotFoundException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity test041() {
        try {
            storageService.readString(LOCAL_FILE_DESTINATION_LIST.get(4), PATH_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity test042() {
        try {
            storageService.readBytes(NO_DESTINATION, OSS_LIST.get(0));
        } catch (StorageException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity test043() {
        try {
            storageService.readBytes(DESTINATION, OSS_LIST.get(5));
        } catch (com.kering.cus.lib.storage.exception.FileNotFoundException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity test044() {
        try {
            storageService.readStream(NO_DESTINATION, OSS_LIST.get(0));
        } catch (StorageException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity test045() {
        try {
            storageService.readStream(DESTINATION, OSS_LIST.get(5));
        } catch (com.kering.cus.lib.storage.exception.FileNotFoundException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc046() {
        boolean exists = storageService.exists(NO_DESTINATION, PATH_LIST.get(0));
        Assert.isTrue(exists==false, "should is false");
        return ResponseEntity.success();
    }

    public ResponseEntity tc047() {
        try {
            storageService.writeString(NULL_STRING, DESTINATION, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc048() {
        try {
            storageService.writeString(CONTEXT, NULL_STRING, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc049() {
        try {
            storageService.writeString(CONTEXT, DESTINATION, NULL_STRING);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc050() {
        try {
            storageService.writeStream(null, DESTINATION, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc051() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        try {
            storageService.writeStream(inputStream, NULL_STRING, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc052() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        try {
            storageService.writeStream(inputStream, DESTINATION, NULL_STRING);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }


    public ResponseEntity tc053() {
        try {
            storageService.writeBytes(null, DESTINATION, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc054() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        try {
            storageService.writeBytes(bytes, NULL_STRING, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc055() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        try {
            storageService.writeBytes(bytes, DESTINATION, NULL_STRING);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc056() {
        try {
            storageService.readString(NULL_STRING, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc057() {
        try {
            storageService.readString(DESTINATION, NULL_STRING);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }


    public ResponseEntity tc058() {
        try {
            storageService.readStream(NULL_STRING, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc059() {
        try {
            storageService.readStream(DESTINATION, NULL_STRING);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc060() {
        try {
            storageService.readBytes(NULL_STRING, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc061() {
        try {
            storageService.readBytes(DESTINATION, NULL_STRING);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc062() {
        try {
            storageService.exists(NULL_STRING, OSS_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc063() {
        try {
            storageService.exists(DESTINATION, NULL_STRING);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc1_1_1() {

        String path = storageService.asyncWriteString(CONTEXT, DESTINATION, OSS_LIST.get(0),true);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        GenericRequestContextHolder.getRequestProperty();
        return ResponseEntity.success(path);
    }
    public ResponseEntity tc1_1_2() {
        String path = storageService.asyncWriteString(CONTEXT, DESTINATION, OSS_LIST.get(0),false);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }


    public ResponseEntity tc1_2_1() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        String path = storageService.asyncWriteStream(inputStream, DESTINATION, OSS_LIST.get(0),true);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }
    public ResponseEntity tc1_2_2() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        String path = storageService.asyncWriteStream(inputStream, DESTINATION, OSS_LIST.get(0),false);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }


    public ResponseEntity tc1_3_1() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        String path = storageService.asyncWriteBytes(bytes, DESTINATION, OSS_LIST.get(0),true);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }
    public ResponseEntity tc1_3_2() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        String path = storageService.asyncWriteBytes(bytes, DESTINATION, OSS_LIST.get(0),false);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }

    public ResponseEntity tc2_1_1() {

        String path = storageService.asyncWriteString(CONTEXT, TASK_CENTER_DESTINATION, OSS_LIST.get(0),true);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        GenericRequestContextHolder.getRequestProperty();
        return ResponseEntity.success(path);
    }
    public ResponseEntity tc2_1_2() {
        String path = storageService.asyncWriteString(CONTEXT, TASK_CENTER_DESTINATION, OSS_LIST.get(0),false);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }


    public ResponseEntity tc2_2_1() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        String path = storageService.asyncWriteStream(inputStream, TASK_CENTER_DESTINATION, OSS_LIST.get(0),true);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }
    public ResponseEntity tc2_2_2() {
        InputStream inputStream = FileUtils.readStreamFromFile(READ_NOT_EMPTY);
        String path = storageService.asyncWriteStream(inputStream, TASK_CENTER_DESTINATION, OSS_LIST.get(0),false);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }


    public ResponseEntity tc2_3_1() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        String path = storageService.asyncWriteBytes(bytes, TASK_CENTER_DESTINATION, OSS_LIST.get(0),true);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }
    public ResponseEntity tc2_3_2() {
        byte[] bytes = FileUtils.readBytesFromFile(READ_NOT_EMPTY);
        String path = storageService.asyncWriteBytes(bytes, TASK_CENTER_DESTINATION, OSS_LIST.get(0),false);
        Assert.isTrue(null != path, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(path);
    }

}
