package com.kering.cus.qa.service;


import com.alibaba.fastjson.JSON;
import com.kering.cus.lib.secret.access.KeyAccessService;
import com.kering.cus.lib.secret.access.dto.DescribeKeyResult;
import com.kering.cus.lib.secret.access.dto.PublicKeyResult;
import com.kering.cus.lib.secret.access.dto.SignResult;
import com.kering.cus.lib.secret.access.exception.KeyAccessException;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.utils.ThrowableUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.nio.charset.StandardCharsets;

import static com.kering.cus.qa.constants.MessageConstants.*;
import static com.kering.cus.qa.constants.SecretManagementSdkConstants.*;

@Service
@ConditionalOnProperty(value = "secret.provider", havingValue = "kms")
public class KmsKeyAccessTestService {

    @Autowired
    KeyAccessService keyAccessService;

    public ResponseEntity tc001() {
        PublicKeyResult result = keyAccessService.getPublicKey(KEY_ID);
        Assert.isTrue(null != result, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc002() {
        try {
            keyAccessService.getPublicKey(null);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }


    public ResponseEntity tc003() {
        SignResult signContent = keyAccessService.sign(KEY_ID, ALGORITHM, TOKEN.getBytes());
        String result = new String(signContent.getSignature(), StandardCharsets.UTF_8);
        Assert.isTrue(null != result, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc004() {
        try {
            keyAccessService.sign(KEY_ID, ALGORITHM, "".getBytes());
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc005() {
        try {
            keyAccessService.sign(KEY_ID, ALGORITHM, null);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc006() {
        try {
            keyAccessService.sign(null, ALGORITHM, TOKEN.getBytes());
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }


    public ResponseEntity tc007() {
        SignResult result = keyAccessService.sign(KEY_ID, null, TOKEN.getBytes());
        Assert.isTrue(result.getSignature().length != 0, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc008() {
        keyAccessService.updateAlias(KEY_ID, ALIASNAME);
        return ResponseEntity.success();
    }

    public ResponseEntity tc009() {
        try {
            keyAccessService.updateAlias(null, ALIASNAME);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc010() {
        try {
            keyAccessService.updateAlias(KEY_ID, null);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }


    public ResponseEntity tc4_1() {
        DescribeKeyResult result = keyAccessService.describeKey(KEY_ID);
        Assert.isTrue(null != result, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success(JSON.toJSONString(result));
    }

    public ResponseEntity tc4_2() {
        try {
            keyAccessService.describeKey("123");
        } catch (KeyAccessException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_3() {
        try {
            keyAccessService.describeKey(NULL_STRING);
        } catch (KeyAccessException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

}
