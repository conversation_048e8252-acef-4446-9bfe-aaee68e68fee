package com.kering.cus.qa.service;

import com.kering.cus.lib.message.queue.producer.Producer;
import com.kering.cus.lib.message.queue.producer.dto.GenericEvent;
import com.kering.cus.qa.entity.OrderEvent;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.entity.TestKafkaMessage;
import com.kering.cus.qa.utils.ThrowableUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.kering.cus.qa.constants.KafkaSdkConstants.*;
import static com.kering.cus.qa.constants.MessageConstants.*;

@Service
public class KafkaSdkTestObjService {


    @Autowired
    KafkaTemplate kafkaTemplate;

    @Autowired
    Producer producer;

    TestKafkaMessage build = new TestKafkaMessage(Thread.currentThread().getStackTrace()[1].getMethodName() + " 发送成功", 1);

    public ResponseEntity tc1_1_1() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_2_1() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, NULL_STRING, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_2_2() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_2_3() {
        try {
            producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, (TestKafkaMessage) null);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc1_2_4() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        try {
            producer.sendMessage(TOPIC_TWO, ORDER_KEY_ONE, content);
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc1_2_5() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        try {
            producer.sendMessage(NULL_STRING, ORDER_KEY_ONE, content);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc1_4_1() {

        CompletableFuture completableFuture = producer.sendMessage(TOPIC_ONE, build);
        try {
            SendResult sendResult = (SendResult) completableFuture.get();
            Object value = sendResult.getProducerRecord().value();
            Assert.isTrue(NULL_STRING != value, EMPTY_MESSAGE);
        } catch (Throwable e) {
            return ResponseEntity.error(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.success();
    }
    public ResponseEntity tc1_4_2() {

        CompletableFuture completableFuture = producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, build);
        try {
            SendResult sendResult = (SendResult) completableFuture.get();
            Object value = sendResult.getProducerRecord().value();
            Assert.isTrue(NULL_STRING != value, EMPTY_MESSAGE);
        } catch (Throwable e) {
            return ResponseEntity.error(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.success();
    }
    public ResponseEntity tc1_4_3() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE_ENCRYPT, build);
        CompletableFuture completableFuture = producer.sendMessage(TOPIC_ONE, content);
        try {
            SendResult sendResult = (SendResult) completableFuture.get();
            Object value = sendResult.getProducerRecord().value();
            Assert.isTrue(NULL_STRING != value, EMPTY_MESSAGE);
        } catch (Throwable e) {
            return ResponseEntity.error(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_4_4() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE_ENCRYPT, build);
        CompletableFuture completableFuture = producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        try {
            SendResult sendResult = (SendResult) completableFuture.get();
            Object value = sendResult.getProducerRecord().value();
            Assert.isTrue(NULL_STRING != value, EMPTY_MESSAGE);
        } catch (Throwable e) {
            return ResponseEntity.error(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.success();
    }

    /**
     * TopicAndGroupListener consumer
     *
     * @return
     */
    public ResponseEntity tc2_1_1_1() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(NULL_STRING, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    /**
     * Comment out AllVarListener and TopicAndGroupListener
     *
     * @return
     */
    public ResponseEntity tc2_1_1_2() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(NULL_STRING, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    /**
     * Comment out AllVarListener and TopicAndGroupListener
     *
     * @return
     */
    public ResponseEntity tc2_1_2_1() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_2_2() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_2_3() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_2_4() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_2_5() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_2_6() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_2_7() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_2_8() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_2_9() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_3_1() {

        ProducerRecord producerRecord = new ProducerRecord(TOPIC_ONE, ORDER_KEY_ONE, build);
        kafkaTemplate.send(producerRecord);
        return ResponseEntity.success();
    }


    public ResponseEntity tc2_1_3_2() {

        ProducerRecord producerRecord = new ProducerRecord(TOPIC_ONE, ORDER_KEY_ONE, build);
        producerRecord.headers().add("eventType", EVENT_TYPE.getBytes());
        kafkaTemplate.send(producerRecord);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_3_3() {

        ProducerRecord producerRecord = new ProducerRecord(TOPIC_ONE, ORDER_KEY_ONE, build);
        producerRecord.headers().add("eventType", EVENT_TYPE.getBytes());
        kafkaTemplate.send(producerRecord);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_3_4() {

        ProducerRecord producerRecord = new ProducerRecord(TOPIC_ONE, ORDER_KEY_ONE, build);
        kafkaTemplate.send(producerRecord);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_4() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE_TEST, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_2_1_1() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_2_1_2() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_2_1_3() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE_ENCRYPT, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_2_1_4() {

        OrderEvent content = new OrderEvent(EVENT_TYPE_ENCRYPT, build, "obj");
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc3_1_1_1() {

        GenericEvent<TestKafkaMessage> content = new GenericEvent<>(EVENT_TYPE_ERROR, build);
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc3_1_1_2() {

        OrderEvent content = new OrderEvent(EVENT_TYPE_ERROR, build, "obj");
        producer.sendMessage(TOPIC_ONE, ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }


    public ResponseEntity tc3_1_1_3() {

        GenericEvent content = new GenericEvent<>(NULL_STRING, build);
        producer.sendMessage(TOPIC_ONE + "_OBJ", ORDER_KEY_ONE, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc3_1_1_4() {

        ProducerRecord producerRecord = new ProducerRecord(TOPIC_ONE + "_OBJ", ORDER_KEY_ONE, build);
        kafkaTemplate.send(producerRecord);
        return ResponseEntity.success();
    }
}
