package com.kering.cus.qa.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kering.cus.lib.common.SortCriteria;
import com.kering.cus.lib.common.context.GenericRequestContextHolder;
import com.kering.cus.lib.persistence.common.entity.PageResult;
import com.kering.cus.qa.dao.isolation.*;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.entity.isolation.*;
import com.kering.cus.qa.service.base.ExecuteService;
import com.kering.cus.qa.utils.BeanCopyUtil;
import com.kering.cus.qa.utils.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

import static com.kering.cus.qa.constants.MybatisSdkConstants.*;

@Service
public class DataIsolationMybatisTestService {


    @Autowired
    UserIsoEnabledDAO userIsoEnabledDAO;

    @Autowired
    UserIsoDisabledDAO userIsoDisabledDAO;

    @Autowired
    UserForModifyIsoEnabledDAO userForModifyIsoEnabledDAO;

    @Autowired
    UserAccountIsoEnabledDAO userAccountIsoEnabledDAO;

    @Autowired
    UserAccountIsoDisabledDAO userAccountIsoDisabledDAO;

    @Autowired
    UserAccountEnabledDisabledDAO userAccountEnabledDisabledDAO;

    public ResponseEntity tc001() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(DEV);

            UserIsoEnabledEntity newEntity = userIsoEnabledDAO.create(createEntity);
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(DEV, TEST_USER));
            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(newEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoEnabledEntity actualUserEntity = optional.get();
            Assert.isTrue(DEV.equals(actualUserEntity.getTenantId()), "tenantId should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc002() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = null;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(QA);

            UserIsoEnabledEntity newEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            Assert.isTrue(createEntity.getTenantId().equals(newEntity.getTenantId()), "tenantId should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc003() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = null;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserIsoEnabledEntity newEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));

        } catch (Exception e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error("should not be able to create entity without tenantId");
    }

    public ResponseEntity tc004() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoDisabledEntity createEntity = new UserIsoDisabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(DEV);

            UserIsoDisabledEntity newEntity = userIsoDisabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            Optional<UserIsoDisabledEntity> optional = userIsoDisabledDAO.findById(newEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoDisabledEntity entity = optional.get();
            Assert.isTrue(DEV.equals(entity.getTenantId()), "tenantId should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc005() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = null;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoDisabledEntity createEntity = new UserIsoDisabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(QA);

            UserIsoDisabledEntity newEntity = userIsoDisabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            Optional<UserIsoDisabledEntity> optional = userIsoDisabledDAO.findById(newEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoDisabledEntity entity = optional.get();
            Assert.isTrue(createEntity.getTenantId().equals(entity.getTenantId()), "tenantId should be equal");

//            AssertionUtils.assertObjectsEqual(createEntity, actualUserEntity);
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc006() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = null;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoDisabledEntity createEntity = new UserIsoDisabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserIsoDisabledEntity newEntity = userIsoDisabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            Optional<UserIsoDisabledEntity> optional = userIsoDisabledDAO.findById(newEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoDisabledEntity entity = optional.get();
            Assert.isTrue(null == entity.getTenantId(), "tenantId should be equal");

//            AssertionUtils.assertObjectsEqual(createEntity, actualUserEntity);
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc007() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserIsoEnabledEntity newEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            newEntity.setName("testName");
            newEntity.setAge(10);
            userIsoEnabledDAO.update(BeanCopyUtil.copyTwo(newEntity));
            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(newEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoEnabledEntity entity = optional.get();
            Assert.isTrue(createEntity.getTenantId().equals(entity.getTenantId()), "tenantId should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc008() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserIsoEnabledEntity newEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            newEntity.setTenantId(DEV);
            userIsoEnabledDAO.update(BeanCopyUtil.copyTwo(newEntity));
            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(newEntity.getId());
            if (!optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc009() {
        try {
            GenericRequestContextHolder.reset();

            UserIsoDisabledEntity createEntity = new UserIsoDisabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(DEV);

            UserIsoDisabledEntity newEntity = userIsoDisabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            newEntity.setTenantId(OTHER);
            userIsoDisabledDAO.update(BeanCopyUtil.copyTwo(newEntity));
            Optional<UserIsoDisabledEntity> optional = userIsoDisabledDAO.findById(newEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoDisabledEntity entity = optional.get();
            Assert.isTrue(newEntity.getTenantId().equals(entity.getTenantId()), "tenantId should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc010() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserIsoEnabledEntity newEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            userIsoEnabledDAO.delete(newEntity);
            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(newEntity.getId());
            if (!optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }


        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc011() {
        try {
            GenericRequestContextHolder.reset();
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(DEV, TEST_USER));
            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(10);
            createEntity.setTenantId(DEV);

            UserIsoEnabledEntity newEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            userIsoEnabledDAO.delete(newEntity);

        } catch (Exception e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error("Memory not specified tenantId error");
    }


    public ResponseEntity tc012() {
        try {
            GenericRequestContextHolder.reset();
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(QA, TEST_USER));
            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(10);
            createEntity.setTenantId(QA);

            UserIsoEnabledEntity newEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            GenericRequestContextHolder.reset();
            userIsoEnabledDAO.delete(newEntity);

        } catch (Exception e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error("Memory not specified tenantId error");
    }

    public ResponseEntity tc013() {
        try {
            GenericRequestContextHolder.reset();

            UserIsoDisabledEntity createEntity = new UserIsoDisabledEntity();
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(10);
            createEntity.setTenantId(DEV);

            UserIsoDisabledEntity newEntity = userIsoDisabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            userIsoDisabledDAO.delete(newEntity);
            Assert.isTrue(!userIsoDisabledDAO.findById(createEntity.getId()).isPresent(), "delete should be 0");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc014() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById("uuid001");
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoEnabledEntity entity = optional.get();
            Assert.isTrue("name001".equals(entity.getName()), "name should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc015() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById("uuid016");
            if (!optional.isEmpty()) {
                return ResponseEntity.error("optional should be null");
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc016() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            Optional<UserIsoDisabledEntity> optional = userIsoDisabledDAO.findById("uuid016");
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional should be null");
            }
            UserIsoDisabledEntity entity = optional.get();
            Assert.isTrue("name016".equals(entity.getName()), "name should be equal");
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc017() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            PageResult<UserIsoEnabledEntity> result = userIsoEnabledDAO.findAll(1, 1000, null);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.error("list no data");
            }
            result.getData().stream().forEach(e -> Assert.isTrue(tenantId.equals(e.getTenantId()), "tenantId should be equal"));

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc018() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = OTHER;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            PageResult<UserIsoEnabledEntity> result = userIsoEnabledDAO.findAll(1, 1000, null);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.success();
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.error("list should no data");
    }

    public ResponseEntity tc019() {
        try {
            GenericRequestContextHolder.reset();
            userIsoEnabledDAO.findAll(1, 1000, null);
        } catch (Exception e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error("list should throw Exception");
    }

    public ResponseEntity tc020() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            SortCriteria sortCriteria = new SortCriteria();
            sortCriteria.getCriteria().put("created_date", "asc");
            PageResult<UserIsoDisabledEntity> result = userIsoDisabledDAO.findAll(1, 40, sortCriteria);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.error("list no data");
            }
            Assert.isTrue(result.getData().size() >= 40, "list size should be more than 45");
            result.getData().stream().forEach(e -> Assert.isTrue(e.getId().contains("uuid"), "tenantId should be equal"));
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc021() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            QueryWrapper<UserIsoEnabledEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", "name001");
            PageResult<UserIsoEnabledEntity> result = userIsoEnabledDAO.findPage(1, 1000, null, queryWrapper);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.error("list no data");
            }
            Assert.isTrue("uuid001".equals(result.getData().get(0).getId()), "name should be equal");
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc022() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            QueryWrapper<UserIsoEnabledEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId);
            PageResult<UserIsoEnabledEntity> result = userIsoEnabledDAO.findPage(1, 1000, null, queryWrapper);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.error("list no data");
            }
            result.getData().stream().forEach(e -> Assert.isTrue(tenantId.equals(e.getTenantId()), "tenantId should be equal"));
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc023() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            QueryWrapper<UserIsoEnabledEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", "name016");
            PageResult<UserIsoEnabledEntity> result = userIsoEnabledDAO.findPage(1, 1000, null, queryWrapper);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.success();
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.error("list should no data");
    }

    public ResponseEntity tc024() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            QueryWrapper<UserIsoEnabledEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", DEV);
            PageResult<UserIsoEnabledEntity> result = userIsoEnabledDAO.findPage(1, 1000, null, queryWrapper);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.success();
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.error("list should no data");
    }

    public ResponseEntity tc025() {
        try {
            GenericRequestContextHolder.reset();
            QueryWrapper<UserIsoEnabledEntity> queryWrapper = new QueryWrapper<>();
            PageResult<UserIsoEnabledEntity> result = userIsoEnabledDAO.findPage(1, 1000, null, queryWrapper);
        } catch (Exception e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error("list should throw Exception");
    }


    public ResponseEntity tc026() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            QueryWrapper<UserIsoDisabledEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", "name001");
            PageResult<UserIsoDisabledEntity> result = userIsoDisabledDAO.findPage(1, 1000, null, queryWrapper);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.error("list no data");
            }
            Assert.isTrue("uuid001".equals(result.getData().get(0).getId()), "name should be equal");
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc027() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            QueryWrapper<UserIsoDisabledEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId);
            PageResult<UserIsoDisabledEntity> result = userIsoDisabledDAO.findPage(1, 1000, null, queryWrapper);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.error("list no data");
            }
            Assert.isTrue(result.getData().size() >= 15, "list size should be equal");
            result.getData().stream().forEach(e -> Assert.isTrue(tenantId.equals(e.getTenantId()), "tenantId should be equal"));
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc028() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            userIsoEnabledDAO.insertUserIsoEnabled(BeanCopyUtil.copyTwo(createEntity));
            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(createEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoEnabledEntity actualUserEntity = optional.get();
            Assert.isTrue(tenantId.equals(actualUserEntity.getTenantId()), "tenantId should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc029() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = null;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(QA);

            userIsoEnabledDAO.insertUserIsoEnabled(BeanCopyUtil.copyTwo(createEntity));
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(QA, TEST_USER));
            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(createEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoEnabledEntity actualUserEntity = optional.get();
            Assert.isTrue(QA.equals(actualUserEntity.getTenantId()), "tenantId should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc030() {
        try {
            GenericRequestContextHolder.reset();

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(QA);

            userIsoEnabledDAO.insertUserIsoEnabled(BeanCopyUtil.copyTwo(createEntity));
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(QA, TEST_USER));
            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(createEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoEnabledEntity actualUserEntity = optional.get();
            Assert.isTrue(QA.equals(actualUserEntity.getTenantId()), "tenantId should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc031() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoDisabledEntity createEntity = new UserIsoDisabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(null);

            userIsoDisabledDAO.insertUserIsoDisabled(BeanCopyUtil.copyTwo(createEntity));
            Optional<UserIsoDisabledEntity> optional = userIsoDisabledDAO.findById(createEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoDisabledEntity actualUserEntity = optional.get();
            Assert.isTrue(tenantId.equals(actualUserEntity.getTenantId()), "tenantId should be null");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc032() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserIsoEnabledEntity actualUserEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            actualUserEntity.setName("testName1");
            actualUserEntity.setAge(30);
            userIsoEnabledDAO.updateUserIsoEnabledById(BeanCopyUtil.copyTwo(actualUserEntity));

            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(createEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoEnabledEntity entity = optional.get();
            Assert.isTrue(actualUserEntity.getName().equals(entity.getName()), "name should be equal");
            Assert.isTrue(actualUserEntity.getAge().equals(entity.getAge()), "age should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc033() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserForModifyIsoEnabledEntity createEntity = new UserForModifyIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserForModifyIsoEnabledEntity actualUserEntity = userForModifyIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            actualUserEntity.setName("TESTTESTTEST");
            actualUserEntity.setAge(40);
            userForModifyIsoEnabledDAO.updateUserForModifyIsoEnabledByTenantId(BeanCopyUtil.copyTwo(actualUserEntity));

            Optional<UserForModifyIsoEnabledEntity> optional = userForModifyIsoEnabledDAO.findById(createEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserForModifyIsoEnabledEntity entity = optional.get();
            Assert.isTrue(actualUserEntity.getName().equals(entity.getName()), "name should be equal");
            Assert.isTrue(actualUserEntity.getAge().equals(entity.getAge()), "age should be equal");
            userForModifyIsoEnabledDAO.delete(entity);

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc034() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserIsoEnabledEntity actualUserEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            actualUserEntity.setAge(40);
            userIsoEnabledDAO.updateUserIsoEnabledByName(BeanCopyUtil.copyTwo(actualUserEntity));

            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(createEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoEnabledEntity entity = optional.get();
            Assert.isTrue(actualUserEntity.getName().equals(entity.getName()), "name should be equal");
            Assert.isTrue(actualUserEntity.getAge().equals(entity.getAge()), "age should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc035() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserForModifyIsoEnabledEntity createEntity = new UserForModifyIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserForModifyIsoEnabledEntity actualUserEntity = userForModifyIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            UserForModifyIsoEnabledEntity setUserEntity = userForModifyIsoEnabledDAO.findById(createEntity.getId()).get();
            setUserEntity.setName("TESTTEST");
            setUserEntity.setAge(40);
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(DEV, TEST_USER));
            userForModifyIsoEnabledDAO.updateUserForModifyIsoEnabledByTenantId(BeanCopyUtil.copyTwo(setUserEntity));
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            Optional<UserForModifyIsoEnabledEntity> optional = userForModifyIsoEnabledDAO.findById(createEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserForModifyIsoEnabledEntity entity = optional.get();
            Assert.isTrue(createEntity.getName().equals(entity.getName()), "name should be equal");
            Assert.isTrue(createEntity.getAge().equals(entity.getAge()), "age should be equal");
            userForModifyIsoEnabledDAO.delete(entity);

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc036() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName("TESTCUSTOMUPDATE");
            createEntity.setAge(10);
            createEntity.setTenantId(tenantId);

            UserIsoEnabledEntity actualUserEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            UserIsoEnabledEntity setUserEntity = userIsoEnabledDAO.findById(createEntity.getId()).get();
            setUserEntity.setAge(40);
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(DEV, TEST_USER));
            userIsoEnabledDAO.updateUserIsoEnabledByName(BeanCopyUtil.copyTwo(setUserEntity));
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(createEntity.getId());
            if (optional.isEmpty()) {
                return ResponseEntity.error("optional is not present");
            }
            UserIsoEnabledEntity entity = optional.get();
            Assert.isTrue(createEntity.getAge().equals(entity.getAge()), "age should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc037() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserIsoEnabledEntity actualUserEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            UserIsoEnabledEntity setUserEntity = userIsoEnabledDAO.findById(createEntity.getId()).get();
            setUserEntity.setName("TESTTEST");
            setUserEntity.setAge(40);
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(DEV, TEST_USER));
            userIsoEnabledDAO.updateUserIsoEnabledById(BeanCopyUtil.copyTwo(setUserEntity));
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            UserIsoEnabledEntity userEntity = userIsoEnabledDAO.findById(createEntity.getId()).get();
            Assert.isTrue(createEntity.getName().equals(userEntity.getName()), "name should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc038() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserIsoEnabledEntity actualUserEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));

            userIsoEnabledDAO.deleteUserIsoEnabledById(actualUserEntity.getId());

            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(actualUserEntity.getId());
            Assert.isTrue(optional.isEmpty(), "user should be deleted");
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc039() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserForModifyIsoEnabledEntity createEntity = new UserForModifyIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserForModifyIsoEnabledEntity actualUserEntity = userForModifyIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));

            userForModifyIsoEnabledDAO.deleteUserForModifyIsoEnabledByTenantId(actualUserEntity.getTenantId());

            Optional<UserForModifyIsoEnabledEntity> optional = userForModifyIsoEnabledDAO.findById(actualUserEntity.getId());
            Assert.isTrue(optional.isEmpty(), "user should be deleted");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc040() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserForModifyIsoEnabledEntity createEntity = new UserForModifyIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserForModifyIsoEnabledEntity actualUserEntity = userForModifyIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));

            userForModifyIsoEnabledDAO.deleteUserForModifyIsoEnabledByName(actualUserEntity.getName());

            Optional<UserForModifyIsoEnabledEntity> optional = userForModifyIsoEnabledDAO.findById(actualUserEntity.getId());
            Assert.isTrue(optional.isEmpty(), "user should be deleted");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc041() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserForModifyIsoEnabledEntity createEntity = new UserForModifyIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserForModifyIsoEnabledEntity actualUserEntity = userForModifyIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));

            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(DEV, TEST_USER));
            userForModifyIsoEnabledDAO.deleteUserForModifyIsoEnabledByTenantId(actualUserEntity.getTenantId());
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            Optional<UserForModifyIsoEnabledEntity> optional = userForModifyIsoEnabledDAO.findById(actualUserEntity.getId());
            UserForModifyIsoEnabledEntity entity = optional.get();
            Assert.isTrue(null != entity, "user not should be deleted");
            Assert.isTrue(createEntity.getName().equals(entity.getName()), "name should be equal");
            Assert.isTrue(createEntity.getAge().equals(entity.getAge()), "age should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc042() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserForModifyIsoEnabledEntity createEntity = new UserForModifyIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserForModifyIsoEnabledEntity actualUserEntity = userForModifyIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(DEV, TEST_USER));
            userForModifyIsoEnabledDAO.deleteUserForModifyIsoEnabledByName(actualUserEntity.getName());
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            Optional<UserForModifyIsoEnabledEntity> optional = userForModifyIsoEnabledDAO.findById(actualUserEntity.getId());
            UserForModifyIsoEnabledEntity entity = optional.get();
            Assert.isTrue(null != entity, "user not should be deleted");
            Assert.isTrue(createEntity.getName().equals(entity.getName()), "name should be equal");
            Assert.isTrue(createEntity.getAge().equals(entity.getAge()), "age should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc043() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));

            UserIsoEnabledEntity createEntity = new UserIsoEnabledEntity();
            createEntity.setId(IdWorker.get32UUID());
            createEntity.setName(RandomUtils.randomString(5, 10));
            createEntity.setAge(RandomUtils.randomNDigitInt(2));
            createEntity.setTenantId(tenantId);

            UserIsoEnabledEntity actualUserEntity = userIsoEnabledDAO.create(BeanCopyUtil.copyTwo(createEntity));
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(DEV, TEST_USER));
            userIsoEnabledDAO.deleteUserIsoEnabledById(actualUserEntity.getName());
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            Optional<UserIsoEnabledEntity> optional = userIsoEnabledDAO.findById(createEntity.getId());
            Assert.isTrue(!optional.isEmpty(), "user not should be deleted");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc044() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            UserIsoEnabledEntity entity = userIsoEnabledDAO.selectUserIsoEnabledById("uuid001");
            Assert.isTrue(null != entity, "user should not be null");

            Assert.isTrue("name001".equals(entity.getName()), "name should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc045() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = DEV;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            UserIsoEnabledEntity entity = userIsoEnabledDAO.selectUserIsoEnabledById("uuid001");

            Assert.isTrue(null == entity, "user should be null");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc046() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserIsoEnabledEntity> results = userIsoEnabledDAO.selectUserIsoEnabledByTenantId(tenantId);

            if (CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("list no data");
            }
            Assert.isTrue(results.size() >= 15, "list size should be equal");
            results.stream().forEach(e -> Assert.isTrue(tenantId.equals(e.getTenantId()), "tenantId should be equal"));

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc047() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = OTHER;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserIsoEnabledEntity> results = userIsoEnabledDAO.selectUserIsoEnabledByTenantId(QA);

            if (!CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("list no data");
            }

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc048() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            UserIsoEnabledEntity entity = userIsoEnabledDAO.selectUserIsoEnabledByName("name001");
            if (null == entity) {
                return ResponseEntity.error("entity is null");
            }
            Assert.isTrue("uuid001".equals(entity.getId()), "id should be equal");

        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc049() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = DEV;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            UserIsoEnabledEntity entity = userIsoEnabledDAO.selectUserIsoEnabledByName("name001");
            if (null != entity) {
                return ResponseEntity.error("entity should be null");
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc050() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = OTHER;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            UserIsoDisabledEntity entity = userIsoDisabledDAO.selectUserIsoDisabledById("uuid001");
            if (null == entity) {
                return ResponseEntity.error("entity is null");
            }
            Assert.isTrue("name001".equals(entity.getName()), "name should be equal");
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc051() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountIsoEnabledEntity> results = userAccountIsoEnabledDAO.selectUserAccountEntityByUserId("uuid001");
            if (CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity is not null");
            }
            results.stream().forEach(e -> {
                Assert.isTrue("name001".equals(e.getUserName()), "name should be equal");
                Assert.isTrue("uuid001".equals(e.getUserId()), "name should be equal");
                Assert.isTrue(e.getAccName().contains("name001"), "name should be equal");
            });
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc052() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = OTHER;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountIsoEnabledEntity> results = userAccountIsoEnabledDAO.selectUserAccountEntityByUserId("uuid001");
            if (!CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity should be null");
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc053() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountIsoEnabledEntity> results = userAccountIsoEnabledDAO.selectUserAccountEntityByUserId("uuid002");
            if (CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity is not null");
            }
            results.stream().forEach(e -> {
                Assert.isTrue("name002".equals(e.getUserName()), "name should be equal");
                Assert.isTrue("uuid002".equals(e.getUserId()), "name should be equal");
                Assert.isTrue(e.getAccName() == null, "name should be equal");
            });
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc054() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = OTHER;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountIsoDisabledEntity> results = userAccountIsoDisabledDAO.selectUserDisabledAccountDisabledEntityByUserId("uuid001");
            if (CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity is not null");
            }
            results.stream().forEach(e -> {
                Assert.isTrue("name001".equals(e.getUserName()), "name should be equal");
                Assert.isTrue("uuid001".equals(e.getUserId()), "name should be equal");
                Assert.isTrue(e.getAccName().contains("name001"), "name should be equal");
            });
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc055() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountEnabledDisabledEntity> results = userAccountEnabledDisabledDAO.selectUserEnabledAccountDisabledEntityByUserId("uuid001");
            if (CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity is not null");
            }
            results.stream().forEach(e -> {
                Assert.isTrue("name001".equals(e.getUserName()), "name should be equal");
                Assert.isTrue("uuid001".equals(e.getUserId()), "name should be equal");
                Assert.isTrue(e.getAccName().contains("name001"), "name should be equal");
            });
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc056() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountEnabledDisabledEntity> results = userAccountEnabledDisabledDAO.selectUserEnabledAccountDisabledEntityByUserId("uuid016");
            if (!CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity should be null");
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc057() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountIsoEnabledEntity> results = userAccountIsoEnabledDAO.selectUserAccountEntityByUserId2("uuid001");
            if (CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity not should be null");
            }
            results.stream().forEach(e -> {
                Assert.isTrue("name001".equals(e.getUserName()), "name should be equal");
                Assert.isTrue("uuid001".equals(e.getUserId()), "name should be equal");
                Assert.isTrue(e.getAccName().contains("name001"), "name should be equal");
            });
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc058() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = OTHER;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountIsoEnabledEntity> results = userAccountIsoEnabledDAO.selectUserAccountEntityByUserId2("uuid001");
            if (!CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity should be null");
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc059() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountIsoEnabledEntity> results = userAccountIsoEnabledDAO.selectUserAccountEntityByUserId2("uuid002");
            if (!CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity should be null");
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc060() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = OTHER;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountIsoDisabledEntity> results = userAccountIsoDisabledDAO.selectUserDisabledAccountDisabledEntityByUserId2("uuid001");
            if (CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity not should be null");
            }
            results.stream().forEach(e -> {
                Assert.isTrue("name001".equals(e.getUserName()), "name should be equal");
                Assert.isTrue("uuid001".equals(e.getUserId()), "name should be equal");
                Assert.isTrue(e.getAccName().contains("name001"), "name should be equal");
            });
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc061() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountEnabledDisabledEntity> results = userAccountEnabledDisabledDAO.selectUserEnabledAccountDisabledEntityByUserId2("uuid002");
            if (CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity is not null");
            }
            results.stream().forEach(e -> {
                Assert.isTrue("name002".equals(e.getUserName()), "name should be equal");
                Assert.isTrue("uuid002".equals(e.getUserId()), "name should be equal");
                Assert.isTrue(e.getAccName().contains("name002"), "name should be equal");
            });
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc062() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = DEV;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            List<UserAccountEnabledDisabledEntity> results = userAccountEnabledDisabledDAO.selectUserEnabledAccountDisabledEntityByUserId("uuid002");
            if (!CollectionUtils.isEmpty(results)) {
                return ResponseEntity.error("entity should be null");
            }
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }


    public ResponseEntity tc063() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            PageResult<UserIsoEnabledEntity> result = userIsoEnabledDAO.findAll(1, 1000, null);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.error("list no data");
            }
            Assert.isTrue(result.getData().size() >= 45, "list size should be greater than 45");
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }

    public ResponseEntity tc064() {
        try {
            GenericRequestContextHolder.reset();
            String tenantId = QA;
            GenericRequestContextHolder.setRequestProperty(ExecuteService.buildRequestProperty(tenantId, TEST_USER));
            PageResult<UserIsoDisabledEntity> result = userIsoDisabledDAO.findAll(1, 1000, null);
            if (CollectionUtils.isEmpty(result.getData())) {
                return ResponseEntity.error("list no data");
            }
            Assert.isTrue(result.getData().size() >= 45, "list size should be greater than 45");
        } catch (Exception e) {
            return ResponseEntity.error(e.getMessage());
        }
        return ResponseEntity.success();
    }
}
