package com.kering.cus.qa.service;

import com.alibaba.fastjson2.JSON;
import com.kering.cus.lib.message.queue.producer.Producer;
import com.kering.cus.lib.message.queue.producer.dto.GenericEvent;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.entity.kafka.AppMessageDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.kering.cus.qa.constants.KafkaSdkConstants.APP_MESSAGE_EVENT_TYPE;
import static com.kering.cus.qa.constants.KafkaSdkConstants.APP_MESSAGE_TOPIC;

@Service
public class KafkaSdkTestAppMessageService {
    @Autowired
    Producer producer;

    public ResponseEntity tc1_1(String params) {
        AppMessageDTO appMessageDTO = JSON.parseObject(params, AppMessageDTO.class);
        GenericEvent<AppMessageDTO> content = new GenericEvent<>(APP_MESSAGE_EVENT_TYPE, appMessageDTO);
        producer.sendMessage(APP_MESSAGE_TOPIC, content);
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_2(AppMessageDTO build) {
        GenericEvent<AppMessageDTO> content = new GenericEvent<>(APP_MESSAGE_EVENT_TYPE, build);
        producer.sendMessage(APP_MESSAGE_TOPIC, content);
        return ResponseEntity.success();
    }
}
