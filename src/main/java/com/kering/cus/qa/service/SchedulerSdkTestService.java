package com.kering.cus.qa.service;

import com.kering.cus.lib.scheduler.enums.JobExecuteMode;
import com.kering.cus.lib.scheduler.enums.JobStatus;
import com.kering.cus.lib.scheduler.enums.JobTimeType;
import com.kering.cus.lib.scheduler.exception.SchedulerException;
import com.kering.cus.lib.scheduler.manager.EasyScheduler;
import com.kering.cus.lib.scheduler.manager.JobManager;
import com.kering.cus.lib.scheduler.manager.model.JobInfo;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.utils.RandomUtils;
import com.kering.cus.qa.utils.ThrowableUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import static com.kering.cus.qa.constants.MessageConstants.*;
import static com.kering.cus.qa.constants.SchedulerSdkConstants.*;

@Service
@Slf4j
public class SchedulerSdkTestService {

    @Autowired
    JobManager jobManager;

    public ResponseEntity tc1_1_1() {
        EasyScheduler.createJob("ANNOTATION_SCHEDULER", JobTimeType.CRON, CRON_EXPRESSION, NULL_ARRAY);
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_1_2() {
        EasyScheduler.createJob("ANNOTATION_SCHEDULER", JobTimeType.FIXED_RATE, FIXED_RATE_EXPRESSION, NULL_ARRAY);
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_1_3() {
        EasyScheduler.createJob("ANNOTATION_SCHEDULER", JobTimeType.FIXED_DELAY, FIXED_DELAY_EXPRESSION, NULL_ARRAY);
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_1_4() {
        EasyScheduler.createJob("ANNOTATION_SCHEDULER", JobTimeType.ONE_TIME, getTime(), NULL_ARRAY);
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_1_5() {
        Long jobId = EasyScheduler.createJob("ANNOTATION_SCHEDULER", JobTimeType.ONE_TIME, getTime(), NULL_ARRAY);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_2_1() {
        Long jobId = EasyScheduler.createJob("ANNOTATION_SCHEDULER", JobTimeType.CRON, CRON_EXPRESSION, NULL_ARRAY);
        EasyScheduler.updateJob(jobId, EasyScheduler.UpdateOption.TIME_EXPRESSION, CRON_EXPRESSION);
        jobManager.deleteJob(jobId);
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_2_2() {
        Long jobId = EasyScheduler.createJob("ANNOTATION_SCHEDULER", JobTimeType.CRON, CRON_EXPRESSION, NULL_ARRAY);
        try {
            EasyScheduler.updateJob(jobId, EasyScheduler.UpdateOption.TIME_EXPRESSION, FIXED_RATE_EXPRESSION);
        } catch (SchedulerException e) {
            jobManager.deleteJob(jobId);
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc2_1_1() {
        JobInfo jobInfo = createJobInfo(JobTimeType.CRON, CRON_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_2() {
        JobInfo jobInfo = createJobInfo(JobTimeType.FIXED_RATE, FIXED_RATE_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_3() {
        JobInfo jobInfo = createJobInfo(JobTimeType.FIXED_DELAY, FIXED_DELAY_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity tc2_1_4() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_5() {
        JobInfo jobInfo = createJobInfo(JobTimeType.CRON, CRON_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        Long jobId1 = jobManager.createJob(jobInfo);
        Assert.isTrue(!jobId.equals(jobId1), NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }


    public ResponseEntity tc2_1_6() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.DISABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(JobStatus.ENABLED.equals(result.getStatus()), NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_1_7() {
        JobInfo jobInfo = createJobInfo(JobTimeType.CRON, FIXED_DELAY_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        try {
            jobManager.createJob(jobInfo);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc2_1_8() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, FIXED_DELAY_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        try {
            jobManager.createJob(jobInfo);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity t2_1_9() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        try {
            jobInfo.setName(NULL_STRING);
            jobManager.createJob(jobInfo);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc2_1_10() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        try {
            jobInfo.setName(EMPTY_STRING);
            jobManager.createJob(jobInfo);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc2_2_3() {
        JobTimeType timeType = JobTimeType.FIXED_RATE;
        String fixedRateExpression = FIXED_RATE_EXPRESSION;
        JobInfo jobInfo = createJobInfo(JobTimeType.CRON, CRON_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobInfo.setJobId(jobId);
        jobInfo.setTimeType(timeType);
        jobInfo.setTimeExpression(fixedRateExpression);
        jobManager.updateJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        Assert.isTrue(timeType.equals(result.getTimeType()), "timeType should be equal");
        Assert.isTrue(fixedRateExpression.equals(result.getTimeExpression()), "timeType should be equal");
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_2_4() {
        JobTimeType timeType = JobTimeType.CRON;
        String fixedRateExpression = CRON_EXPRESSION;
        JobInfo jobInfo = createJobInfo(JobTimeType.FIXED_RATE, FIXED_RATE_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobInfo.setJobId(jobId);
        jobInfo.setTimeType(timeType);
        jobInfo.setTimeExpression(fixedRateExpression);
        jobManager.updateJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        Assert.isTrue(timeType.equals(result.getTimeType()), "timeType should be equal");
        Assert.isTrue(fixedRateExpression.equals(result.getTimeExpression()), "timeType should be equal");
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_2_5() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobInfo.setJobId(jobId);
        jobInfo.setClassMethod(NULL_CLASS_METHOD);
        jobManager.updateJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(NULL_CLASS_METHOD.equals(result.getClassMethod()), "classMethod should be equal");
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_2_6() {
        JobInfo jobInfo = createJobInfo(JobTimeType.CRON, CRON_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobInfo.setJobId(jobId);
        try {
            jobInfo.setTimeExpression(FIXED_DELAY_EXPRESSION);
            jobManager.updateJob(jobInfo);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }


    public ResponseEntity tc2_2_7() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobInfo.setJobId(jobId);
        try {
            jobInfo.setTimeExpression(FIXED_DELAY_EXPRESSION);
            jobManager.updateJob(jobInfo);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc2_2_8() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobInfo.setJobId(jobId);
        String name = jobInfo.getName();
        jobInfo.setName(NULL_STRING);
        jobManager.updateJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(name.equals(result.getName()), NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_2_9() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobInfo.setJobId(jobId);
        String name = jobInfo.getName();
        jobInfo.setName(EMPTY_STRING);
        jobManager.updateJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(name.equals(result.getName()), NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity test2_2_10(String args) {
        Long jobId = parseParams(args);
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        try {
            jobInfo.setJobId(jobId);
            jobManager.updateJob(jobInfo);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc2_3_1() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_3_2() {
        try {
            jobManager.getJobInfo(123l);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc2_4_1() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobManager.disableJob(jobId);
        jobManager.enableJob(jobId);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        Assert.isTrue(JobStatus.ENABLED.equals(result.getStatus()), "status should be equal");
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_4_2() {
        try {
            jobManager.enableJob(123l);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity test2_4_3() {
        JobInfo jobInfo = createJobInfo(JobTimeType.CRON, CRON_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        threadSleep();
        try {
            jobManager.enableJob(jobId);
        } catch (SchedulerException e) {
            log.error("test case" + Thread.currentThread().getStackTrace()[1].getMethodName() + " success");
            jobManager.deleteJob(jobId);
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        log.error("test case" + Thread.currentThread().getStackTrace()[1].getMethodName() + " failed, jobId:{}", jobId);
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity test2_4_4(String args) {
        Long jobId = parseParams(args);
        try {
            jobManager.enableJob(jobId);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc2_5_1() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobManager.disableJob(jobId);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        Assert.isTrue(JobStatus.DISABLED.equals(result.getStatus()), "status should be equal");
        return ResponseEntity.success();
    }

    public ResponseEntity test2_5_2() {
        JobInfo jobInfo = createJobInfo(JobTimeType.CRON, CRON_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        threadSleep();
        jobManager.disableJob(jobId);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        Assert.isTrue(JobStatus.DISABLED.equals(result.getStatus()), "status should be equal");
        jobManager.deleteJob(jobId);
        log.error("test case" + Thread.currentThread().getStackTrace()[1].getMethodName() + " success");
        return ResponseEntity.success();
    }


    public ResponseEntity tc2_5_3() {
        JobInfo jobInfo = createJobInfo(JobTimeType.FIXED_RATE, FIXED_RATE_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobManager.disableJob(jobId);
        try {
            jobManager.disableJob(jobId);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc2_5_4() {
        try {
            jobManager.disableJob(123l);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity test2_5_5(String args) {
        Long jobId = parseParams(args);
        try {
            jobManager.disableJob(jobId);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc2_6_1() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        jobManager.deleteJob(jobId);
        try {
            jobManager.getJobInfo(jobId);
        } catch (SchedulerException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(EMPTY_MESSAGE);
    }

    public ResponseEntity test2_6_2() {
        JobInfo jobInfo = createJobInfo(JobTimeType.CRON, CRON_EXPRESSION, JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        threadSleep();
        jobManager.deleteJob(jobId);
        try {
            jobManager.getJobInfo(jobId);
        } catch (SchedulerException e) {
            log.error("test case " + Thread.currentThread().getStackTrace()[1].getMethodName() + " success");
            return ResponseEntity.success();
        }
        return ResponseEntity.error(EMPTY_MESSAGE);
    }

    public ResponseEntity tc2_6_3() {

        try {
            jobManager.deleteJob(123l);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity test2_6_4(String args) {
        Long jobId = parseParams(args);
        try {
            jobManager.deleteJob(jobId);
        } catch (SchedulerException e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc3_1() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc3_2() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, EMPTY_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc3_3() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_METHOD, new Object[]{1});
//        updateJobInfoClass(jobInfo, CLASS_NAME,CLASS_METHOD,"{\"num\":1}");
        Long jobId = jobManager.createJob(jobInfo);
        return ResponseEntity.success(Thread.currentThread().getStackTrace()[1].getMethodName() + "  : " + jobId.toString());
    }

    public ResponseEntity tc3_4() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_PARAM_METHOD, new Object[]{1});
        Long jobId = jobManager.createJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc3_5() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_PARAM_METHOD, new Object[]{1, "test"});
        Long jobId = jobManager.createJob(jobInfo);
        return ResponseEntity.success(Thread.currentThread().getStackTrace()[1].getMethodName() + "  : " + jobId.toString());
    }

    public ResponseEntity tc3_7() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_PARAM_METHOD, EMPTY_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        return ResponseEntity.success(Thread.currentThread().getStackTrace()[1].getMethodName() + "  : " + jobId.toString());
    }

    public ResponseEntity tc3_8() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_PARAM_METHOD, new Object[]{"test"});
        Long jobId = jobManager.createJob(jobInfo);
        return ResponseEntity.success(Thread.currentThread().getStackTrace()[1].getMethodName() + "  : " + jobId.toString());
    }

    public ResponseEntity tc3_9() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_MORE_PARAM_METHOD, new Object[]{1, "test"});
        Long jobId = jobManager.createJob(jobInfo);
        JobInfo result = jobManager.getJobInfo(jobId);
        Assert.isTrue(result != null, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc3_10() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_MORE_PARAM_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        return ResponseEntity.success(Thread.currentThread().getStackTrace()[1].getMethodName() + "  : " + jobId.toString());
    }

    public ResponseEntity tc3_13() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, CLASS_MORE_PARAM_METHOD, new Object[]{"test", 1});
        Long jobId = jobManager.createJob(jobInfo);
        return ResponseEntity.success(Thread.currentThread().getStackTrace()[1].getMethodName() + "  : " + jobId.toString());
    }

    public ResponseEntity tc3_14() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, NULL_CLASS_NAME, CLASS_PARAM_METHOD, new Object[]{1});
        Long jobId = jobManager.createJob(jobInfo);
        return ResponseEntity.success(Thread.currentThread().getStackTrace()[1].getMethodName() + "  : " + jobId.toString());
    }

    public ResponseEntity tc3_15() {
        JobInfo jobInfo = createJobInfo(JobTimeType.ONE_TIME, getTime(), JobStatus.ENABLED);
        updateJobInfoClass(jobInfo, CLASS_NAME, NULL_CLASS_METHOD, NULL_ARRAY);
        Long jobId = jobManager.createJob(jobInfo);
        return ResponseEntity.success(Thread.currentThread().getStackTrace()[1].getMethodName() + "  : " + jobId.toString());
    }


    public JobInfo createJobInfo(JobTimeType jobTimeType, String timeExpression, JobStatus jobStatus) {
        JobInfo jobInfo = new JobInfo();
        jobInfo.setName("testCase" + String.valueOf(RandomUtils.randomNDigitInt(3)));
        jobInfo.setTimeType(jobTimeType);
        jobInfo.setTimeExpression(timeExpression);
        jobInfo.setExecuteMode(JobExecuteMode.STANDALONE);
        jobInfo.setStatus(jobStatus);
        return jobInfo;
    }

    public void updateJobInfoClass(JobInfo jobInfo, String className, String classMethod, Object[] parameters) {
        jobInfo.setClassName(className);
        jobInfo.setClassMethod(classMethod);
        jobInfo.setParameters(parameters);
    }

    public String getTime() {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, 1);
        return df.format(calendar.getTime());
    }


    public void threadSleep() {
        try {
            Thread.sleep(1000 * 60 * 2);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }


    public Long parseParams(String args) {
        return Long.valueOf(args);
    }


}
