package com.kering.cus.qa.service;

import com.kering.cus.qa.dao.lock.TestOrderLockDao;
import com.kering.cus.qa.dao.lock.TestOrderNoLockDao;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.entity.lock.TestOrderEntityLock;
import com.kering.cus.qa.entity.lock.TestOrderEntityNoLock;
import com.kering.cus.qa.entity.orm.TestOrderEntity;
import com.kering.cus.qa.entity.sqlCondition.ConditionType;
import com.kering.cus.qa.entity.sqlCondition.SqlCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class MybatisLockTestService {
    @Autowired
    TestOrderLockDao testOrderLockDao;
    @Autowired
    TestOrderNoLockDao testOrderNoLockDao;

    public ResponseEntity tc_pse_1_1() {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        Optional<TestOrderEntityLock> optionalTestOrderEntity2= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int updateCount= testOrderLockDao.updateById(testOrderEntity1);
        Assert.isTrue(updateCount == 1, "update first failed");
        TestOrderEntityLock testOrderEntity2=optionalTestOrderEntity2.get();
        testOrderEntity2.setPrice("80");
        updateCount= testOrderLockDao.updateById(testOrderEntity2);
        Assert.isTrue(updateCount == 0, "update second failed");

        Optional<TestOrderEntityLock> optionalTestOrderEntity= testOrderLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.get().getPrice().equals("79") &&optionalTestOrderEntity.get().getVersion()==2, "update second failed");

        return ResponseEntity.success();
    }

    public ResponseEntity tc_pse_1_2() {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        Optional<TestOrderEntityLock> optionalTestOrderEntity2= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("81");
        int updateCount= testOrderLockDao.updatePriceByWhere(testOrderEntity1,  "79");
        Assert.isTrue(updateCount == 1, "update first failed");

        TestOrderEntityLock testOrderEntity2=optionalTestOrderEntity2.get();
        testOrderEntity2.setPrice("82");
        updateCount= testOrderLockDao.updatePriceByWhere(testOrderEntity2,  "81");
        Assert.isTrue(updateCount == 0, "update second failed");

        Optional<TestOrderEntityLock> optionalTestOrderEntity= testOrderLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.get().getPrice().equals("81"), "update second failed");
        Assert.isTrue(optionalTestOrderEntity.get().getVersion()==3, "update second failed");

        return ResponseEntity.success();
    }

    public ResponseEntity tc_pse_1_3() {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("82");



        int updateCount= testOrderLockDao.updatePriceByVersionEqual(testOrderEntity1,  3);
        Assert.isTrue(updateCount == 1, "update first failed");
        Optional<TestOrderEntityLock> optionalTestOrderEntity= testOrderLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.get().getPrice().equals("82"), "update second failed");
        Assert.isTrue(optionalTestOrderEntity.get().getVersion()==4, "update second failed");

        return ResponseEntity.success();
    }
    public ResponseEntity tc_pse_1_4() {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("85");

        int updateCount= testOrderLockDao.updatePriceByVersionGreaterThan(testOrderEntity1,  3);
        Assert.isTrue(updateCount == 0, "update first failed");
        Optional<TestOrderEntityLock> optionalTestOrderEntity= testOrderLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.get().getPrice().equals("82"), "update second failed");
        Assert.isTrue(optionalTestOrderEntity.get().getVersion()==4, "update second failed");

        return ResponseEntity.success();
    }
    public ResponseEntity tc_pse_1_5() {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("86");

        int updateCount= testOrderLockDao.updatePriceByVersionLessThan(testOrderEntity1,  3);
        Assert.isTrue(updateCount == 0, "update first failed");
        Optional<TestOrderEntityLock> optionalTestOrderEntity= testOrderLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.get().getPrice().equals("82"), "update second failed");
        Assert.isTrue(optionalTestOrderEntity.get().getVersion()==4, "update second failed");

        return ResponseEntity.success();
    }

    public ResponseEntity tc_pse_2_1() {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        Optional<TestOrderEntityLock> optionalTestOrderEntity2= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int updateCount= testOrderLockDao.updateById(testOrderEntity1);
        Assert.isTrue(updateCount == 1, "update first failed");
        TestOrderEntityLock testOrderEntity2=optionalTestOrderEntity2.get();
        int deleteCount= testOrderLockDao.deleteById(testOrderEntity2);
        Assert.isTrue(deleteCount == 0, "update second failed");
        return ResponseEntity.success(updateCount+"--"+deleteCount);
    }

    public ResponseEntity tc_pse_2_2() {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int deteteCount= testOrderLockDao.deletePriceByWhere(testOrderEntity1,"79");
        Assert.isTrue(deteteCount == 1, "update first failed");
        Optional<TestOrderEntityLock> optionalTestOrderEntity= testOrderLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.isEmpty(), "update second failed");

        return ResponseEntity.success();
    }

    public ResponseEntity tc_pse_2_3() {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int deteteCount= testOrderLockDao.deletePriceByVersionEqual(testOrderEntity1,3);
        Assert.isTrue(deteteCount == 1, "update first failed");
        Optional<TestOrderEntityLock> optionalTestOrderEntity= testOrderLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.isEmpty(), "update second failed");

        return ResponseEntity.success();
    }
    public ResponseEntity tc_pse_2_4() {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int deteteCount= testOrderLockDao.deletePriceByVersionGreaterThan(testOrderEntity1,3);
        Assert.isTrue(deteteCount == 1, "update first failed");
        Optional<TestOrderEntityLock> optionalTestOrderEntity= testOrderLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.isEmpty(), "update second failed");

        return ResponseEntity.success();
    }
    public ResponseEntity tc_pse_2_5() {
        Optional<TestOrderEntityLock> optionalTestOrderEntity1= testOrderLockDao.findById(18);
        TestOrderEntityLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int deteteCount= testOrderLockDao.deletePriceByVersionLessThan(testOrderEntity1,3);
        Assert.isTrue(deteteCount == 1, "update first failed");
        Optional<TestOrderEntityLock> optionalTestOrderEntity= testOrderLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.isEmpty(), "update second failed");

        return ResponseEntity.success();
    }


    public ResponseEntity tc_pse_3_1() {
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity1= testOrderNoLockDao.findById(18);
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity2= testOrderNoLockDao.findById(18);
        TestOrderEntityNoLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int updateCount= testOrderNoLockDao.updateById(testOrderEntity1);
        Assert.isTrue(updateCount == 1, "update first failed");
        TestOrderEntityNoLock testOrderEntity2=optionalTestOrderEntity2.get();
        testOrderEntity2.setPrice("80");
        updateCount= testOrderNoLockDao.updateById(testOrderEntity2);
        Assert.isTrue(updateCount == 1, "update second failed");

        Optional<TestOrderEntityNoLock> optionalTestOrderEntity= testOrderNoLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.get().getPrice().equals("79") &&optionalTestOrderEntity.get().getVersion()==2, "update second failed");

        return ResponseEntity.success();
    }

    public ResponseEntity tc_pse_3_2() {
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity1= testOrderNoLockDao.findById(18);
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity2= testOrderNoLockDao.findById(18);
        TestOrderEntityNoLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("81");
        int updateCount= testOrderNoLockDao.updatePriceByWhere(testOrderEntity1,  "79");
        Assert.isTrue(updateCount == 1, "update first failed");
        TestOrderEntityNoLock testOrderEntity2=optionalTestOrderEntity2.get();
        testOrderEntity2.setPrice("82");
        updateCount= testOrderNoLockDao.updatePriceByWhere(testOrderEntity2,  "81");
        Assert.isTrue(updateCount == 0, "update second failed");

        Optional<TestOrderEntityNoLock> optionalTestOrderEntity= testOrderNoLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.get().getPrice().equals("81"), "update second failed");
        Assert.isTrue(optionalTestOrderEntity.get().getVersion()==3, "update second failed");

        return ResponseEntity.success();
    }

    public ResponseEntity tc_pse_3_3() {
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity1= testOrderNoLockDao.findById(18);
        TestOrderEntityNoLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("82");
        int updateCount= testOrderNoLockDao.updatePriceByVersionEqual(testOrderEntity1,  3);
        Assert.isTrue(updateCount == 1, "update first failed");
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity= testOrderNoLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.get().getPrice().equals("82"), "update second failed");
        Assert.isTrue(optionalTestOrderEntity.get().getVersion()==4, "update second failed");

        return ResponseEntity.success();
    }
    public ResponseEntity tc_pse_3_4() {
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity1= testOrderNoLockDao.findById(18);
        TestOrderEntityNoLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("85");

        int updateCount= testOrderNoLockDao.updatePriceByVersionGreaterThan(testOrderEntity1,  3);
        Assert.isTrue(updateCount == 0, "update first failed");
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity= testOrderNoLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.get().getPrice().equals("82"), "update second failed");
        Assert.isTrue(optionalTestOrderEntity.get().getVersion()==4, "update second failed");

        return ResponseEntity.success();
    }
    public ResponseEntity tc_pse_3_5() {
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity1= testOrderNoLockDao.findById(18);
        TestOrderEntityNoLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("86");

        int updateCount= testOrderNoLockDao.updatePriceByVersionLessThan(testOrderEntity1,  3);
        Assert.isTrue(updateCount == 0, "update first failed");
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity= testOrderNoLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.get().getPrice().equals("82"), "update second failed");
        Assert.isTrue(optionalTestOrderEntity.get().getVersion()==4, "update second failed");

        return ResponseEntity.success();
    }


    public ResponseEntity tc_pse_4_1() {
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity1= testOrderNoLockDao.findById(18);
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity2= testOrderNoLockDao.findById(18);
        TestOrderEntityNoLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int updateCount= testOrderNoLockDao.updateById(testOrderEntity1);
        Assert.isTrue(updateCount == 1, "update first failed");
        TestOrderEntityNoLock testOrderEntity2=optionalTestOrderEntity2.get();
        int deleteCount= testOrderNoLockDao.deleteById(testOrderEntity2);
        Assert.isTrue(deleteCount == 1, "delete failed");
        return ResponseEntity.success();
    }

    public ResponseEntity tc_pse_4_2() {
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity1= testOrderNoLockDao.findById(18);
        TestOrderEntityNoLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int deteteCount= testOrderNoLockDao.deletePriceByWhere(testOrderEntity1,"79");
        Assert.isTrue(deteteCount == 1, "update first failed");
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity= testOrderNoLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.isEmpty(), "update second failed");

        return ResponseEntity.success();
    }

    public ResponseEntity tc_pse_4_3() {
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity1= testOrderNoLockDao.findById(18);
        TestOrderEntityNoLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int deteteCount= testOrderNoLockDao.deletePriceByVersionEqual(testOrderEntity1,3);
        Assert.isTrue(deteteCount == 1, "update first failed");
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity= testOrderNoLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.isEmpty(), "update second failed");

        return ResponseEntity.success();
    }
    public ResponseEntity tc_pse_4_4() {
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity1= testOrderNoLockDao.findById(18);
        TestOrderEntityNoLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int deteteCount= testOrderNoLockDao.deletePriceByVersionGreaterThan(testOrderEntity1,3);
        Assert.isTrue(deteteCount == 1, "update first failed");
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity= testOrderNoLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.isEmpty(), "update second failed");

        return ResponseEntity.success();
    }
    public ResponseEntity tc_pse_4_5() {
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity1= testOrderNoLockDao.findById(18);
        TestOrderEntityNoLock testOrderEntity1=optionalTestOrderEntity1.get();
        testOrderEntity1.setPrice("79");
        int deteteCount= testOrderNoLockDao.deletePriceByVersionLessThan(testOrderEntity1,3);
        Assert.isTrue(deteteCount == 1, "update first failed");
        Optional<TestOrderEntityNoLock> optionalTestOrderEntity= testOrderNoLockDao.findById(18);
        Assert.isTrue(optionalTestOrderEntity.isEmpty(), "update second failed");

        return ResponseEntity.success();
    }
}
