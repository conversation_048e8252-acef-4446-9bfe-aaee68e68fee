package com.kering.cus.qa.service.base;


import com.alibaba.fastjson2.JSON;
import com.kering.cus.lib.common.PlatformHeaders;
import com.kering.cus.lib.common.context.GenericRequestContextHolder;
import com.kering.cus.lib.common.context.bo.RequestProperty;
import com.kering.cus.qa.common.TestCaseAnnotation;
import com.kering.cus.qa.constants.FileStorageSdkConstants;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.entity.kafka.AppMessageDTO;
import com.kering.cus.qa.service.KafkaSdkTestAppMessageService;
import com.kering.cus.qa.utils.ThrowableUtils;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

import static com.kering.cus.qa.constants.MybatisSdkConstants.DEFAULT_USER;
import static com.kering.cus.qa.constants.MybatisSdkConstants.QA;

@Service
@Slf4j
public class ExecuteService {

    @Autowired
    private ApplicationContext applicationContext;

    public ResponseEntity executeServiceMethod(String serviceName, String methodName, String params) {
        try {
            if(GenericRequestContextHolder.getRequestProperty().isEmpty()){
                GenericRequestContextHolder.setRequestProperty(buildRequestProperty(QA, DEFAULT_USER));
            }
            List<ResponseEntity> responseEntities = new ArrayList<>();
            ResponseEntity result = ResponseEntity.success();
            Object service = applicationContext.getBean(serviceName);
            if (Objects.isNull(methodName)) {
                Method[] methods = service.getClass().getDeclaredMethods();
                Method[] tcMethods = Arrays.stream(methods)
                        .filter(m -> m.getName().startsWith("tc"))
                        .toArray(Method[]::new);
                Arrays.sort(tcMethods, Comparator.comparing(Method::getName));
                for (Method method : tcMethods) {
                    responseEntities.add(setTestAnnotation(service, method, result, null));
                }
            } else {
                Method method;
                if (!StringUtils.isBlank(params) && !"null".equals(params)) {
                    method = service.getClass().getMethod(methodName, String.class);
                } else {
                    method = service.getClass().getMethod(methodName);
                }

                return setTestAnnotation(service, method, result, params);
            }
            int successCount = responseEntities.stream().filter(ResponseEntity::getIsSuccess).toList().size();
            result.setCaseData("Case Run result: " + responseEntities.size() + "/" + successCount + "/" + (responseEntities.size() - successCount));
            result.setData(responseEntities);
            return result;
        } catch (Exception e) {
            log.error("throw exception :", e);
            return new ResponseEntity(false, e.getMessage(), null, null);
        }
    }

    private ResponseEntity setTestAnnotation(Object service, Method method, ResponseEntity result, String params) {
        ResponseEntity resultData;
        try {
            if (!StringUtils.isBlank(params) && !"null".equals(params)) {
                resultData = (ResponseEntity) method.invoke(service, params);
            } else {
                resultData = (ResponseEntity) method.invoke(service);
            }
        } catch (InvocationTargetException e) {
            log.error("execute method throw InvocationTargetException :", e);
            resultData = ResponseEntity.error(e.getMessage() + e.getTargetException().getCause());
        } catch (IllegalAccessException e) {
            log.error("execute method throw IllegalAccessException :", e);
            resultData = ResponseEntity.error(e.getMessage() + e.getCause());
        }

        Map<String, Object> errorResponse = new HashMap<>();
        if (method.isAnnotationPresent(TestCaseAnnotation.class)) {
            TestCaseAnnotation testCaseAnnotation = method.getAnnotation(TestCaseAnnotation.class);
            errorResponse.put("testSet", testCaseAnnotation.testSet());
            errorResponse.put("summary", testCaseAnnotation.summary());
            errorResponse.put("caseNumber", testCaseAnnotation.caseNumber());
        }
        resultData.setCaseData(errorResponse);
        if (!resultData.getIsSuccess()) {
            result.setIsSuccess(false);
            result.setMessage((result.getMessage() == null ? "" : result.getMessage()) + "/" + method.getName());
        }
        return resultData;
    }

    public static RequestProperty buildRequestProperty(String tenantId, String loginName) {
        RequestProperty requestProperty = new RequestProperty();
        requestProperty.setProperty(PlatformHeaders.TENANT_ID.name(), tenantId);
        requestProperty.setProperty(PlatformHeaders.EMPLOYEE_EMAIL.name(), loginName);
        return requestProperty;
    }

    public ResponseEntity sendAppMessage(AppMessageDTO message) {
        KafkaSdkTestAppMessageService bean = applicationContext.getBean(KafkaSdkTestAppMessageService.class);
        try {
            return bean.tc1_2(message);
        }catch (Throwable e){
            return ResponseEntity.error(ThrowableUtils.getStackTrace(e));
        }
    }

}
