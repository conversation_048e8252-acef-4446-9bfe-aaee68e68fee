package com.kering.cus.qa.service;

import com.kering.cus.lib.secret.access.exception.SecretAccessException;
import com.kering.cus.lib.secret.access.exception.SecretNotFoundException;
import com.kering.cus.lib.secret.access.exception.SecretSerializationException;
import com.kering.cus.lib.secret.access.implementation.KMSSecretAccessService;
import com.kering.cus.lib.secret.access.implementation.LocalFileSecretAccessService;
import com.kering.cus.lib.secret.access.implementation.PropertySecretAccessService;
import com.kering.cus.lib.secret.access.implementation.VaultSecretAccessService;
import com.kering.cus.qa.constants.SecretManagementSdkConstants;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.entity.orm.UserConfig;
import com.kering.cus.qa.entity.orm.UserTitleConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.source.InvalidConfigurationPropertyNameException;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashSet;
import java.util.Set;

import static com.kering.cus.qa.constants.MessageConstants.*;
import static com.kering.cus.qa.constants.SecretManagementSdkConstants.KEY_LIST;

@Service
@Slf4j
public class SecretManagementSdkTestService {


    @Autowired
    private ApplicationContext applicationContext;

    private String pathOrKeyNull = null;
    private String pathOrKeyEmpty = "";

    //    @Autowired
//    @Qualifier("propertySecretAccessService")
    PropertySecretAccessService propertySecretAccessService;

    @Autowired
//    @Qualifier("kMSSecretAccessService")
    KMSSecretAccessService kmsSecretAccessService;

    //    @Autowired
//    @Qualifier("localFileSecretAccessService")
    LocalFileSecretAccessService localFileSecretAccessService;

//    @Autowired
    VaultSecretAccessService vaultSecretAccessService;

    @Value("${SECRET_ACCESS_ROOT_PATH:null}")
    private String secretRootPath;


    /**
     * test set:  KMS-getSecretValue(String path,Class<T> type)
     * Summary: Gets the field configuration in the Class as normal and serializes it
     */
    public ResponseEntity tc001() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(0);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = kmsSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: KMS-getSecretValue(String path,Class<T> type)
     * Summary: The certificate key has fewer fields than the Class, gets the configuration and serializes normally
     */
    public ResponseEntity tc002() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(1);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = kmsSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPort()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: KMS-getSecretValue(String path,Class<T> type)
     * Summary: The certificate key has more fields than the Class, gets the configuration and serializes as normal
     */
    public ResponseEntity tc003() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\",\"url\":\"testUrl\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(2);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = kmsSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: KMS-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case -path is empty
     */
    public ResponseEntity tc004() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = pathOrKeyNull;

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Asserts that the exception type is NullPointerException
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-getSecretValue(String path,Class<T> type)
     * Summary: The exception case -path is an empty string
     */
    public ResponseEntity tc005() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = pathOrKeyEmpty;

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (IllegalArgumentException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("IllegalArgumentException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-getSecretValue(String path,Class<T> type)
     * Summary: The file corresponding to the exception use case -key does not exist
     */
    public ResponseEntity tc006() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(3);

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (IllegalArgumentException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("IllegalArgumentException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case - integer conversion serialization exception
     */
    public ResponseEntity tc007() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000a\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(4);

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (SecretSerializationException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretSerializationException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case - Certificate does not exist
     */
    public ResponseEntity tc008() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = null;
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(5);

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (SecretAccessException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretAccessException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public String getSecretValue(String path，String key)
     * Summary: Gets the configuration normally and returns a non-null value
     */
    public ResponseEntity tc009() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(0);
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,UserConfig.class);
            String name = kmsSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(true);
            //2.Asserts the output string name
            if (!SecretManagementSdkConstants.USER.getName().equals(name)) {
                responseEntity.setMessage("User names are inconsistent：" + name);
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: KMS-public String getSecretValue(String path，String key)
     * Summary: Exception use case -key pass empty string
     */
    public ResponseEntity tc010() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(0);
            String key = pathOrKeyEmpty;

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2. Catch the call exception in step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public String getSecretValue(String path，String key)
     * Summary: Exception use case -path is empty
     */
    public ResponseEntity tc011() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = pathOrKeyNull;
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2. Catch the call exception in step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public String getSecretValue(String path，String key)
     * Summary: The exception case -path is an empty string
     */
    public ResponseEntity tc012() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = pathOrKeyEmpty;
            String key = pathOrKeyNull;

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (IllegalArgumentException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("IllegalArgumentException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public String getSecretValue(String path，String key)
     * Summary: Exception use case -key is null
     */
    public ResponseEntity tc013() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(0);
            String key = pathOrKeyNull;

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(true);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public String getSecretValue(String path，String key)
     * Summary: Exception use case -path and key are empty
     */
    public ResponseEntity tc014() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = pathOrKeyNull;
            String key = pathOrKeyNull;

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public String getSecretValue(String path，String key)
     * Summary: The file corresponding to the exception use case -key does not exist
     */
    public ResponseEntity tc015() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(3);
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (IllegalArgumentException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("IllegalArgumentException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public String getSecretValue(String path，String key)
     * Summary: Exception use case -key does not exist
     */
    public ResponseEntity tc016() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(0);
            String key = KEY_LIST.get(2);

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public String getSecretValue(String path，String key)
     * Summary: Exception use case - Certificate does not exist
     */
    public ResponseEntity tc017() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = null;
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(5);
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,UserConfig.class);
            kmsSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretAccessException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretAccessException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Specify all fields, get the configuration and serialize as normal
     */
    public ResponseEntity tc018() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(0);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Specify all fields, the certificate value configuration has redundant keys, the normal configuration and serialization
     */
    public ResponseEntity tc019() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\",\"url\":\"testUrl\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(2);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Empty field collection, normal fetch empty object
     */
    public ResponseEntity tc020() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(2);

            Set<String> keys = new HashSet<>();

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3. Assert that the output object name, password, and port values are correct
            if (null != userConfig.getName()) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPassword()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPort()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Specify some fields, get the configuration and serialize normally
     */
    public ResponseEntity tc021() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(2);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPort()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case -path is empty
     */
    public ResponseEntity tc022() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = pathOrKeyNull;

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: The exception case -path is an empty string
     */
    public ResponseEntity tc023() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = pathOrKeyEmpty;

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (IllegalArgumentException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("IllegalArgumentException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case -keys is empty
     */
    public ResponseEntity tc024() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(0);

            Set<String> keys = null;

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use cases -path and keys are empty
     */
    public ResponseEntity tc025() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = pathOrKeyNull;

            Set<String> keys = null;

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case - integer conversion serialization exception
     */
    public ResponseEntity tc026() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000a\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(4);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretSerializationException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretSerializationException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public String getSecretValue(String path，String key)
     * Summary: The file corresponding to the exception use case -key does not exist
     */
    public ResponseEntity tc027() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(3);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (IllegalArgumentException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("IllegalArgumentException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case - specifies that the certificate key does not exist
     */
    public ResponseEntity tc028() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(1);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: KMS-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case - Certificate does not exist
     */
    public ResponseEntity tc029() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = null;
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(5);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            kmsSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretAccessException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretAccessException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-getSecretValue(String path,Class<T> type)
     * Summary: Get the configuration and serialize as normal
     */
    public ResponseEntity tc030() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(0);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = propertySecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: properties-getSecretValue(String path,Class<T> type)
     * Summary: The certificate key has fewer fields than the Class, gets the configuration and serializes normally
     */
    public ResponseEntity tc031() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(1);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = propertySecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPort()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: properties-getSecretValue(String path,Class<T> type)
     * Summary: The certificate key has more fields than the Class, gets the configuration and serializes as normal
     */
    public ResponseEntity tc032() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(2);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = propertySecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: properties-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case -path is empty
     */
    public ResponseEntity tc033() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyNull;

            //1.Call getSecretValue(path,UserConfig.class);
            propertySecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-getSecretValue(String path,Class<T> type)
     * Summary: The exception case -path is an empty string
     */
    public ResponseEntity tc034() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyEmpty;

            //1.Call getSecretValue(path,UserConfig.class);
            propertySecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretSerializationException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretSerializationException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-getSecretValue(String path,Class<T> type)
     * Summary: The file corresponding to the exception use case -key does not exist
     */
    public ResponseEntity tc035() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(3);

            //1.Call getSecretValue(path,UserConfig.class);
            propertySecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (InvalidConfigurationPropertyNameException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("InvalidConfigurationPropertyNameException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case - the certificate's key is missing a value value
     */
    public ResponseEntity tc036() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(4);

            //1.Call getSecretValue(path,UserConfig.class);
            propertySecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (Throwable e) {
            responseEntity.setIsSuccess(true);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case - integer conversion serialization exception
     */
    public ResponseEntity tc037() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(5);

            //1.Call getSecretValue(path,UserConfig.class);
            propertySecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (Throwable e) {
            responseEntity.setIsSuccess(true);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case - Get empty certificate exception
     */
    public ResponseEntity tc038() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(6);

            //1.Call getSecretValue(path,UserConfig.class);
            propertySecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (Throwable e) {
            responseEntity.setIsSuccess(true);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: Specifies the key, gets the specified configuration, and returns a non-null value
     */
    public ResponseEntity tc039() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(0);
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,key);
            String name = propertySecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(true);
            //2.Asserts the output string name
            if (!SecretManagementSdkConstants.USER.getName().equals(name)) {
                responseEntity.setMessage("User names are inconsistent：" + name);
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: Exception use case - Specifies the empty string key
     */
    public ResponseEntity tc040() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(0);
            String key = pathOrKeyEmpty;

            //1.Call getSecretValue(path,key);
            propertySecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: Exception use case -path is empty
     */
    public ResponseEntity tc041() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyNull;
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,key);
            propertySecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: The exception case -path is an empty string
     */
    public ResponseEntity tc042() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyEmpty;
            String key = pathOrKeyNull;

            //1.Call getSecretValue(path,key);
            propertySecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: Exception use case -key is null
     */
    public ResponseEntity tc043() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(0);
            String key = pathOrKeyNull;

            //1.Call getSecretValue(path,key);
            propertySecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: Exception use case -path and key are empty
     */
    public ResponseEntity tc044() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyNull;
            String key = pathOrKeyNull;

            //1.Call getSecretValue(path,key);
            propertySecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: The file corresponding to the exception use case -key does not exist
     */
    public ResponseEntity tc045() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(3);
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,key);
            propertySecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);

            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: Exception use case -key does not exist
     */
    public ResponseEntity tc046() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(0);
            String key = KEY_LIST.get(2);

            //1.Call getSecretValue(path,key);
            propertySecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: Exception use case - the certificate's key is missing a value value
     */
    public ResponseEntity tc047() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(4);
            String key = KEY_LIST.get(3);

            //1.Call getSecretValue(path,key);
            String port = propertySecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            responseEntity.setData(port);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: Exception use case - Get empty certificate exception
     */
    public ResponseEntity tc048() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(6);
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,key);
            propertySecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Specify all fields, get the configuration and serialize as normal
     */
    public ResponseEntity tc049() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(0);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Specify all fields, the certificate value configuration has redundant keys, the normal configuration and serialization
     */
    public ResponseEntity tc050() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(2);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Empty field collection, normal fetch empty object
     */
    public ResponseEntity tc051() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(0);

            Set<String> keys = new HashSet<>();

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3. Assert that the output object name, password, and port values are correct
            if (null != userConfig.getName()) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPassword()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPort()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Specify some fields, get the configuration and serialize normally
     */
    public ResponseEntity tc052() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(0);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPort()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case -path is empty
     */
    public ResponseEntity tc053() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyNull;

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: The exception case -path is an empty string
     */
    public ResponseEntity tc054() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyEmpty;

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case -keys is empty
     */
    public ResponseEntity tc055() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(0);

            Set<String> keys = null;

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use cases -path and keys are empty
     */
    public ResponseEntity tc056() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyNull;

            Set<String> keys = null;

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: The file corresponding to the exception use case -key does not exist
     */
    public ResponseEntity tc057() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(3);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case - integer conversion serialization exception
     */
    public ResponseEntity tc058() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(5);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Asserts that the output object name, password, and port values are correct
            // -------- Expectation --------------
        } catch (SecretSerializationException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretSerializationException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case - The specified key is not configured in the certificate
     */
    public ResponseEntity tc059() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(1);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public String getSecretValue(String path，String key)
     * Summary: Exception use case - The key of the certificate is not configured with a value value
     */
    public ResponseEntity tc060() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(4);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            responseEntity.setData(userConfig);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case - Get empty certificate exception
     */
    public ResponseEntity tc061() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.PROPERTY_PATH_LIST.get(6);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            propertySecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }


    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: Gets the field configuration in the Class as normal and serializes it
     */
    public ResponseEntity tc062() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(0);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = localFileSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: The file key has fewer fields than the Class, gets the configuration and serializes normally
     */
    public ResponseEntity tc063() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(1);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = localFileSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: The file key has more fields than the Class. The configuration is obtained and serialized as normal
     */
    public ResponseEntity tc064() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(2);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = localFileSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: The key name is the hump name, the file is named with uppercase underscore, and the configuration is obtained and serialized normally
     */
    public ResponseEntity tc065() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(6);

            //1.Call getSecretValue(path,UserConfig.class);
            UserTitleConfig userTitleConfig = localFileSecretAccessService.getSecretValue(path, UserTitleConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER_TITLE.getName().equals(userTitleConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userTitleConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER_TITLE.getPassword().equals(userTitleConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userTitleConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER_TITLE.getPort().equals(userTitleConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userTitleConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER_TITLE.getDbName().equals(userTitleConfig.getDbName())) {
                responseEntity.setMessage("The database names are inconsistent：" + userTitleConfig.getDbName());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: The key name is the name of the hump, the file name is uppercase without underscores, and the obtained value is empty
     */
    public ResponseEntity tc066() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(7);

            //1.Call getSecretValue(path,UserConfig.class);
            UserTitleConfig userTitleConfig = localFileSecretAccessService.getSecretValue(path, UserTitleConfig.class);
            responseEntity.setIsSuccess(true);
            //2.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER_TITLE.getName().equals(userTitleConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userTitleConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER_TITLE.getPassword().equals(userTitleConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userTitleConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER_TITLE.getPort().equals(userTitleConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userTitleConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            if (null != userTitleConfig.getDbName()) {
                responseEntity.setMessage("The database names are inconsistent：" + userTitleConfig.getDbName());
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case -key name hump name, file name hump name
     */
    public ResponseEntity tc067() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(8);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, UserTitleConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case -path is empty
     */
    public ResponseEntity tc068() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyNull;

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: The exception case -path is an empty string
     */
    public ResponseEntity tc069() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyEmpty;

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: The file corresponding to the exception use case -key does not exist
     */
    public ResponseEntity tc070() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(9);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case - the contents of the file are empty
     */
    public ResponseEntity tc071() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(10);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }


    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case - integer conversion serialization exception
     */
    public ResponseEntity tc072() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(4);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretSerializationException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretSerializationException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case - No file credentials exist
     */
    public ResponseEntity tc073() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(5);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public String getSecretValue(String path，String key)
     * Summary: Gets the configuration normally and returns a non-null value
     */
    public ResponseEntity tc074() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(0);
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,key);
            String name = localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(true);
            //2.Asserts the output string name
            if (!SecretManagementSdkConstants.USER.getName().equals(name)) {
                responseEntity.setMessage("User names are inconsistent：" + name);
                responseEntity.setIsSuccess(false);
            }
            //3.Catch the call exception from step 2 and assert the exception
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-public String getSecretValue(String path，String key)
     * Summary: Exception use case - specifies that key is an empty string
     */
    public ResponseEntity tc075() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(0);
            String key = pathOrKeyEmpty;

            //1.Call getSecretValue(path,key);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);

            //3.Catch the call exception from step 2 and assert the exception
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: key names the hump, files are named with uppercase underscores, and the configuration is obtained and serialized normally
     */
    public ResponseEntity tc076() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(6);
            String key = KEY_LIST.get(0);

            //1.Call getSecretValue(path,UserConfig.class);
            String dbName = localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(true);
            //2.Asserts the output string dbName
            if (!SecretManagementSdkConstants.USER_TITLE.getDbName().equals(dbName)) {
                responseEntity.setMessage("The database names are inconsistent：" + dbName);
                responseEntity.setIsSuccess(false);
            }

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case -key names the hump and files are named in uppercase without underscores
     */
    public ResponseEntity tc077() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(7);
            String key = KEY_LIST.get(0);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-getSecretValue(String path,Class<T> type)
     * Summary: Exception use case -key names the hump and file names the hump
     */
    public ResponseEntity tc078() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(8);
            String key = KEY_LIST.get(0);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public String getSecretValue(String path，String key)
     * Summary: Exception use case -path is empty
     */
    public ResponseEntity tc079() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = pathOrKeyNull;
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public String getSecretValue(String path，String key)
     * Summary: The exception case -path is an empty string
     */
    public ResponseEntity tc080() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = pathOrKeyEmpty;
            String key = pathOrKeyNull;

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public String getSecretValue(String path，String key)
     * Summary: Exception use case -key is null
     */
    public ResponseEntity tc081() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(0);
            String key = pathOrKeyNull;

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }


    /**
     * test set: LocalFile-public String getSecretValue(String path，String key)
     * Summary: Exception use case -path and key are empty
     */
    public ResponseEntity tc082() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = pathOrKeyNull;
            String key = pathOrKeyNull;

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public String getSecretValue(String path，String key)
     * Summary: Abnormal use case -path is too long (>=256 characters)
     */
    public ResponseEntity tc083() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(9);
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public String getSecretValue(String path，String key)
     * Summary: The file corresponding to the exception use case -key does not exist
     */
    public ResponseEntity tc084() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(0);
            String key = KEY_LIST.get(2);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set:LocalFile-public String getSecretValue(String path，String key)
     * Summary: The contents of the file corresponding to the exception use case -key are empty
     */
    public ResponseEntity tc085() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(10);
            String key = KEY_LIST.get(3);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public String getSecretValue(String path，String key)
     * Summary: Exception use case - File credentials do not exist
     */
    public ResponseEntity tc086() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(5);
            String key = KEY_LIST.get(1);

            //1.Call getSecretValue(path,UserConfig.class);
            localFileSecretAccessService.getSecretValue(path, key);
            responseEntity.setIsSuccess(false);
            //2.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Specify all fields, get the configuration and serialize as normal
     */
    public ResponseEntity tc087() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(0);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: The file key has more fields than the Class. The configuration is obtained and serialized as normal
     */
    public ResponseEntity tc088() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(2);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPort().equals(userConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Empty field collection, normal fetch empty object
     */
    public ResponseEntity tc089() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(0);

            Set<String> keys = new HashSet<>();

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3. Assert that the output object name, password, and port values are correct
            if (null != userConfig.getName()) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPassword()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPort()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: keys is a collection of some fields in the Class. Only these fields have values in the output result
     */
    public ResponseEntity tc090() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(0);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserConfig userConfig = localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //3.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER.getName().equals(userConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER.getPassword().equals(userConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (null != userConfig.getPort()) {
                responseEntity.setMessage("Password inconsistency：" + userConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: In Class, key is the name of the hump, the file is capitalized underscore, and the configuration is obtained and serialized normally
     */
    public ResponseEntity tc091() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(6);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");
            keys.add("dbName");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            UserTitleConfig userTitleConfig = localFileSecretAccessService.getSecretValue(path, keys, UserTitleConfig.class);
            responseEntity.setIsSuccess(true);
            //3.Asserts that the output object name, password, and port values are correct
            if (!SecretManagementSdkConstants.USER_TITLE.getName().equals(userTitleConfig.getName())) {
                responseEntity.setMessage("User names are inconsistent：" + userTitleConfig.getName());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER_TITLE.getPassword().equals(userTitleConfig.getPassword())) {
                responseEntity.setMessage("Password inconsistency：" + userTitleConfig.getPassword());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER_TITLE.getPort().equals(userTitleConfig.getPort())) {
                responseEntity.setMessage("Password inconsistency：" + userTitleConfig.getPort());
                responseEntity.setIsSuccess(false);
            }
            if (!SecretManagementSdkConstants.USER_TITLE.getDbName().equals(userTitleConfig.getDbName())) {
                responseEntity.setMessage("The database names are inconsistent：" + userTitleConfig.getDbName());
                responseEntity.setIsSuccess(false);
            }
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }


    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: In the exception case -Class, key is named for the hump, and the file is named in uppercase without underscores
     */
    public ResponseEntity tc092() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(7);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");
            keys.add("dbName");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserTitleConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: In exception case -Class, key names the hump and file names the hump
     */
    public ResponseEntity tc093() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(8);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");
            keys.add("dbName");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserTitleConfig.class);
            responseEntity.setIsSuccess(false);
            //3.Catch the call exception from step 2 and assert the exception
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case -path is empty
     */
    public ResponseEntity tc094() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyNull;

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //3.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: The exception case -path is an empty string
     */
    public ResponseEntity tc095() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyEmpty;

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //3.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case -keys is empty
     */
    public ResponseEntity tc096() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(0);

            Set<String> keys = null;

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //3.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use cases -path and keys are empty
     */
    public ResponseEntity tc097() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = pathOrKeyNull;

            Set<String> keys = null;

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //3.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("NullPointerException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case - integer conversion serialization exception
     */
    public ResponseEntity tc098() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(4);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //3.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (SecretSerializationException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretSerializationException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: The file corresponding to the exception use case -key does not exist
     */
    public ResponseEntity tc099() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(1);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //3.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: properties-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Abnormal use case -path is too long (>=256 characters)
     */
    public ResponseEntity tc100() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(9);

            Set<String> keys = null;

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //3.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (NullPointerException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("IllegalArgumentException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public String getSecretValue(String path，String key)
     * Summary: The contents of the file corresponding to the exception use case -key are empty
     */
    public ResponseEntity tc101() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(10);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //3.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set: LocalFile-public <T> T getSecretValue(String path,Set<String> keys,Class<T> type)
     * Summary: Exception use case - No file credentials exist
     */
    public ResponseEntity tc102() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            String path = SecretManagementSdkConstants.FILE_PATH_LIST.get(5);

            Set<String> keys = new HashSet<>();
            keys.add("name");
            keys.add("password");
            keys.add("port");

            //2.Call getSecretValue (path, keys, UserConfig. Class);
            localFileSecretAccessService.getSecretValue(path, keys, UserConfig.class);
            responseEntity.setIsSuccess(false);

            //3.Asserts that the exception type is IllegalArgumentException
            // -------- Expectation --------------
        } catch (SecretNotFoundException e) {
            responseEntity.setIsSuccess(true);
            responseEntity.setMessage("SecretNotFoundException :" + e.getMessage());
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        // -------- test action end --------------
        return responseEntity;
    }

    /**
     * test set:  KMS-getSecretValue(String path,Class<T> type)
     * Summary: Change the name to testName, and check again in an hour to see if the KEY changes normally
     */
    public ResponseEntity tc103() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            //long l = System.currentTimeMillis();

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":"+String.valueOf(l)+",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(11);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = kmsSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            //2. Obtain the values of name, password, and port
            responseEntity.setData(userConfig);
            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    /**
     * test set:  KMS-getSecretValue(String path,Class<T> type)
     * Summary: Delete the name and check whether the KEY is deleted one hour later
     */
    public ResponseEntity tc104() {
        ResponseEntity responseEntity = new ResponseEntity();
        try {

            // -------- test action start --------------
            //InitBeanConfig.MockSecretManagerClient.key = "{\"name\":\"testname\",\"password\":\"testpassword\",\"port\":\"1000\"}";
            String path = SecretManagementSdkConstants.KMS_PATH_LIST.get(12);

            //1.Call getSecretValue(path,UserConfig.class);
            UserConfig userConfig = kmsSecretAccessService.getSecretValue(path, UserConfig.class);
            responseEntity.setIsSuccess(true);
            responseEntity.setData(userConfig);

            // -------- test action end --------------
            // -------- Expectation --------------
        } catch (Throwable e) {
            log.error("throw exception :", e);
            responseEntity.setIsSuccess(false);
            String stackTrace = getStackTrace(e);
            responseEntity.setMessage(e.getClass().getName() + " :" + stackTrace);
        }
        return responseEntity;
    }

    public ResponseEntity tc4_1_1_1() {
        String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(0);
        UserConfig userConfig = vaultSecretAccessService.getSecretValue(path, UserConfig.class);
        Assert.isTrue(null != userConfig, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc4_1_1_2() {
        String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(1);
        UserConfig userConfig = vaultSecretAccessService.getSecretValue(path, UserConfig.class);
        Assert.isTrue(null != userConfig, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc4_1_1_3() {
        String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(2);
        UserConfig userConfig = vaultSecretAccessService.getSecretValue(path, UserConfig.class);
        Assert.isTrue(null != userConfig, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc4_1_2_1() {
        try {
            String path = NULL_STRING;
            vaultSecretAccessService.getSecretValue(path, UserConfig.class);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_1_2_2() {
        try {
            String path = EMPTY_STRING;
            vaultSecretAccessService.getSecretValue(path, UserConfig.class);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_1_2_3() {
        try {
            String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(3);
            vaultSecretAccessService.getSecretValue(path, UserConfig.class);
        } catch (SecretNotFoundException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }
    public ResponseEntity tc4_1_2_4() {
        try {
            String path = secretRootPath;
            vaultSecretAccessService.getSecretValue(path, UserConfig.class);
        } catch (SecretAccessException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_1_2_5() {
        try {
            String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(4);
            vaultSecretAccessService.getSecretValue(path, UserConfig.class);
        } catch (SecretSerializationException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_2_1_1() {
        String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(0);
        String key = KEY_LIST.get(1);
        String secretValue = vaultSecretAccessService.getSecretValue(path, key);
        Assert.isTrue(null != secretValue, NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc4_2_2_1() {
        try {
            String path = NULL_STRING;
            String key = KEY_LIST.get(1);
            vaultSecretAccessService.getSecretValue(path, key);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_2_2_2() {
        try {
            String path = EMPTY_STRING;
            String key = KEY_LIST.get(1);
            vaultSecretAccessService.getSecretValue(path, key);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_2_2_3() {
        try {
            String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(3);
            String key = KEY_LIST.get(1);
            vaultSecretAccessService.getSecretValue(path, key);
        } catch (SecretNotFoundException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }
    public ResponseEntity tc4_2_2_4() {
        try {
            String path = secretRootPath;
            String key = KEY_LIST.get(1);
            vaultSecretAccessService.getSecretValue(path, key);
        } catch (SecretAccessException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_2_2_5() {
        try {
            String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(0);
            String key = NULL_STRING;
            vaultSecretAccessService.getSecretValue(path, key);
        } catch (SecretNotFoundException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_2_2_6() {
        try {
            String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(0);
            String key = EMPTY_STRING;
            vaultSecretAccessService.getSecretValue(path, key);
        } catch (SecretNotFoundException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_2_2_7() {
        try {
            String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(0);
            String key = KEY_LIST.get(2);
            vaultSecretAccessService.getSecretValue(path, key);
        } catch (SecretNotFoundException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_2_2_8() {
        try {
            String path = NULL_STRING;
            String key = NULL_STRING;
            vaultSecretAccessService.getSecretValue(path, key);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_3_1_1() {
        String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(0);
        Set<String> keys = new HashSet<>();
        keys.add(KEY_LIST.get(1));
        keys.add(KEY_LIST.get(3));
        keys.add(KEY_LIST.get(4));
        UserConfig entity = vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        Assert.isTrue(null != entity, NOT_EMPTY_MESSAGE);
        checkField(entity.getName(), "name");
        checkField(entity.getPassword(), "password");
        checkField(entity.getPort(), "port");
        return ResponseEntity.success();
    }

    public ResponseEntity tc4_3_1_2() {
        String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(0);
        Set<String> keys = new HashSet<>();
        keys.add(KEY_LIST.get(1));
        keys.add(KEY_LIST.get(3));
        UserConfig entity = vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        Assert.isTrue(null != entity, NOT_EMPTY_MESSAGE);
        Assert.isTrue(null == entity.getPassword(), NOT_EMPTY_MESSAGE);
        checkField(entity.getName(), "name");
        checkField(entity.getPort(), "port");
        return ResponseEntity.success();
    }
    public ResponseEntity tc4_3_1_3() {
        String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(0);
        Set<String> keys = new HashSet<>();
        UserConfig entity = vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        Assert.isTrue(null == entity.getName(), NOT_EMPTY_MESSAGE);
        Assert.isTrue(null == entity.getPassword(), NOT_EMPTY_MESSAGE);
        Assert.isTrue(null == entity.getPort(), NOT_EMPTY_MESSAGE);
        return ResponseEntity.success();
    }

    public ResponseEntity tc4_3_2_1() {
        try {
            String path = NULL_STRING;
            Set<String> keys = new HashSet<>();
            keys.add(KEY_LIST.get(1));
            keys.add(KEY_LIST.get(3));
            keys.add(KEY_LIST.get(4));
            vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_3_2_2() {
        try {
            String path = EMPTY_STRING;
            Set<String> keys = new HashSet<>();
            keys.add(KEY_LIST.get(1));
            keys.add(KEY_LIST.get(3));
            keys.add(KEY_LIST.get(4));
            vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_3_2_3() {
        try {
            String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(3);
            Set<String> keys = new HashSet<>();
            keys.add(KEY_LIST.get(1));
            keys.add(KEY_LIST.get(3));
            keys.add(KEY_LIST.get(4));
            vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        } catch (SecretNotFoundException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }
    public ResponseEntity tc4_3_2_4() {
        try {
            String path = secretRootPath;
            Set<String> keys = new HashSet<>();
            keys.add(KEY_LIST.get(1));
            keys.add(KEY_LIST.get(3));
            keys.add(KEY_LIST.get(4));
            vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        } catch (SecretAccessException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_3_2_5() {
        try {
            String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(0);
            Set<String> keys = null;
            vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        } catch (NullPointerException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_3_2_6() {
        try {
            String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(0);
            Set<String> keys = new HashSet<>();
            keys.add(KEY_LIST.get(2));
            vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        } catch (SecretNotFoundException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_3_2_7() {
        try {
            String path = NULL_STRING;
            Set<String> keys = null;
            vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    public ResponseEntity tc4_3_2_8() {
        try {
            String path = secretRootPath + SecretManagementSdkConstants.VAULT_PATH_LIST.get(4);
            Set<String> keys = new HashSet<>();
            keys.add(KEY_LIST.get(1));
            keys.add(KEY_LIST.get(3));
            keys.add(KEY_LIST.get(4));
            vaultSecretAccessService.getSecretValue(path, keys, UserConfig.class);
        } catch (SecretSerializationException e) {
            return ResponseEntity.success();
        }
        return ResponseEntity.error(THROW_EXCEPTION);
    }

    private static void checkField(Object field, String fieldName) {
        if (field == null) {
            throw new IllegalArgumentException(NOT_EMPTY_MESSAGE + ": " + fieldName + " is null");
        }
        if (field instanceof String && ((String) field).isEmpty()) {
            throw new IllegalArgumentException(NOT_EMPTY_MESSAGE + ": " + fieldName + " is an empty string");
        }
    }

    // 获取完整的异常栈信息
    public static String getStackTrace(Throwable throwable) {
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        throwable.printStackTrace(printWriter);
        return stringWriter.toString();
    }
}
