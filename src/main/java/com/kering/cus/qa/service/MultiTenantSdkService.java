package com.kering.cus.qa.service;

import com.kering.cus.lib.common.PlatformHeaders;
import com.kering.cus.lib.common.context.bo.IdentityContext;
import com.kering.cus.lib.common.context.bo.RequestProperty;
import com.kering.cus.lib.common.context.bo.TenantContext;
import com.kering.cus.lib.common.context.bo.TraceContext;
import com.kering.cus.qa.entity.MultiTenantEntity;
import com.kering.cus.qa.entity.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestClient;

import java.util.function.Consumer;

import static com.kering.cus.qa.constants.MessageConstants.NULL_STRING;
import static com.kering.cus.qa.constants.MultiTenantSdkConstants.*;

@Service
public class MultiTenantSdkService {

    RestClient defaultClient = RestClient.builder().baseUrl("http://127.0.0.1:9100/multiTenant/").build();

    public ResponseEntity tc1_1() {
        requestOktaAllHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        RequestProperty result = multiTenantEntity.getRequestProperty();
        result.getRequestAttributeMaps().forEach((k, v) -> Assert.hasText(v,k+":"+ v +" is empty"));
        return ResponseEntity.success();
    }
    public ResponseEntity tc1_2() {
        requestOktaAllHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        TenantContext result = multiTenantEntity.getTenantContext();
        Assert.hasText(result.getTenantId(), "tenantId is empty");
        return ResponseEntity.success();
    }
    public ResponseEntity tc1_3() {
        requestOktaAllHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        IdentityContext result = multiTenantEntity.getIdentityContext();
        Assert.hasText(result.getLoginName(), "loginName is empty");
        Assert.hasText(result.getSource(), "source is empty");
        Assert.hasText(result.getUserId(), "userId is empty");
        Assert.hasText(result.getWeComEmail(), "wecomEmail is empty");
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_4() {
        requestOktaAllHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        TraceContext result = multiTenantEntity.getTraceContext();
        Assert.hasText(result.getClientId(), "clientId is empty");
        Assert.hasText(result.getRequestId(), "requestId is empty");
        Assert.hasText(result.getGatewayRequestId(), "gatewayRequestId is empty");
        Assert.hasText(result.getEagleEyeTraceId(), "eagleEyeTraceId is empty");
        return ResponseEntity.success();
    }


    public ResponseEntity tc1_5() {
        requestOktaPartHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(PART_PARAM_KEY);
        RequestProperty result = multiTenantEntity.getRequestProperty();
        result.getRequestAttributeMaps().forEach((k, v) -> {
            //TENANT_ID,EMPLOYEE_EMAIL,CLIENT_ID
                if(k.equals(PlatformHeaders.TENANT_ID) || k.equals(PlatformHeaders.EMPLOYEE_EMAIL) || k.equals(PlatformHeaders.CLIENT_ID)){
                    Assert.isTrue(NULL_STRING == v, k+"is not empty");
                }else{
                    Assert.hasText(v,v+" is empty");
                }
            }
        );
        return ResponseEntity.success();
    }
    public ResponseEntity tc1_6() {
        requestOktaPartHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(PART_PARAM_KEY);
        TenantContext result = multiTenantEntity.getTenantContext();
        Assert.isTrue(NULL_STRING == result.getTenantId(), "tenantId is not empty");
        return ResponseEntity.success();
    }
    public ResponseEntity tc1_7() {
        requestOktaPartHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(PART_PARAM_KEY);
        IdentityContext result = multiTenantEntity.getIdentityContext();
        Assert.isTrue(NULL_STRING == result.getLoginName(), "loginName is not empty");
        Assert.hasText(result.getSource(), "source is empty");
        Assert.hasText(result.getUserId(), "userId is empty");
        Assert.hasText(result.getWeComEmail(), "wecomEmail is empty");
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_8() {
        requestOktaPartHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(PART_PARAM_KEY);
        TraceContext result = multiTenantEntity.getTraceContext();
        Assert.isTrue(NULL_STRING == result.getClientId(), "clientId is not empty");
        Assert.hasText(result.getRequestId(), "requestId is empty");
        Assert.hasText(result.getGatewayRequestId(), "gatewayRequestId is empty");
        Assert.hasText(result.getEagleEyeTraceId(), "eagleEyeTraceId is empty");
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_9() {
        requestOktaEmptyHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(EMPTY_PARAM_KEY);
        RequestProperty result = multiTenantEntity.getRequestProperty();
        result.getRequestAttributeMaps().forEach((k, v) -> Assert.isTrue(NULL_STRING == v,k+":"+ v +" is empty"));
        return ResponseEntity.success();
    }
    public ResponseEntity tc1_10() {
        requestOktaEmptyHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(EMPTY_PARAM_KEY);
        TenantContext result = multiTenantEntity.getTenantContext();
        Assert.isTrue(NULL_STRING == result.getTenantId(), "tenantId is not empty");
        return ResponseEntity.success();
    }
    public ResponseEntity tc1_11() {
        requestOktaEmptyHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(EMPTY_PARAM_KEY);
        IdentityContext result = multiTenantEntity.getIdentityContext();
        Assert.isTrue(NULL_STRING == result.getLoginName(), "loginName is not empty");
        Assert.isTrue(NULL_STRING == result.getSource(), "source is not empty");
        Assert.isTrue(NULL_STRING == result.getUserId(), "userId is not empty");
        Assert.isTrue(NULL_STRING == result.getWeComEmail(), "wecomEmail is not empty");
        return ResponseEntity.success();
    }

    public ResponseEntity tc1_12() {
        requestOktaEmptyHeaders();
        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(EMPTY_PARAM_KEY);
        TraceContext result = multiTenantEntity.getTraceContext();
        Assert.isTrue(NULL_STRING == result.getClientId(), "clientId is empty");
        Assert.isTrue(NULL_STRING == result.getRequestId(), "requestId is not empty");
        Assert.isTrue(NULL_STRING == result.getGatewayRequestId(), "gatewayRequestId is not empty");
        Assert.isTrue(NULL_STRING == result.getEagleEyeTraceId(), "eagleEyeTraceId is not empty");
        return ResponseEntity.success();
    }



    public ResponseEntity tc2_1() {
        requestOktaAllHeadersToEmail("okta",HEADER_MAP.get(PlatformHeaders.EMPLOYEE_EMAIL.getValue()));

        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        IdentityContext result = multiTenantEntity.getIdentityContext();
        Assert.hasText(result.getLoginName(), "loginName is not empty");
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_2() {
        try {
            requestOktaAllHeadersToEmail("okta",NULL_STRING);
        }catch (Throwable e){
            return ResponseEntity.success();
        }
        return ResponseEntity.error("should throw exception");
    }

    public ResponseEntity tc2_3() {
        try {
            requestOktaAllHeadersToEmail(NULL_STRING,NULL_STRING);
        }catch (Throwable e){
            return ResponseEntity.success();
        }
        return ResponseEntity.error("should throw exception");
    }

    public ResponseEntity tc2_4() {
        requestOktaAllHeadersToEmail(NULL_STRING,HEADER_MAP.get(PlatformHeaders.EMPLOYEE_EMAIL.getValue()));

        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        IdentityContext result = multiTenantEntity.getIdentityContext();
        Assert.hasText(result.getLoginName(), "loginName is not empty");
        return ResponseEntity.success();
    }


    public ResponseEntity tc2_5() {
        requestOktaAllHeadersToEmail(HEADER_MAP.get(PlatformHeaders.SOURCE.getValue()),HEADER_MAP.get(PlatformHeaders.EMPLOYEE_EMAIL.getValue()));

        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        IdentityContext result = multiTenantEntity.getIdentityContext();
        Assert.hasText(result.getLoginName(), "loginName is not empty");
        return ResponseEntity.success();
    }

    public ResponseEntity tc2_6() {
        try {
            requestOktaAllHeadersToEmail(HEADER_MAP.get(PlatformHeaders.SOURCE.getValue()),NULL_STRING);
        }catch (Throwable e){
            return ResponseEntity.success();
        }
        return ResponseEntity.error("should throw exception");
    }


    public ResponseEntity tc3_1() {
        requestOktaAllHeadersToUserId("wechat",HEADER_MAP.get(PlatformHeaders.USER_ID.getValue()));

        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        IdentityContext result = multiTenantEntity.getIdentityContext();
        Assert.hasText(result.getUserId(), "userId is not empty");
        return ResponseEntity.success();
    }

    public ResponseEntity tc3_2() {
        try {
            requestOktaAllHeadersToUserId("wechat",NULL_STRING);
        }catch (Throwable e){
            return ResponseEntity.success();
        }
        return ResponseEntity.error("should throw exception");
    }

    public ResponseEntity tc3_3() {
        requestOktaAllHeadersToUserId("wecom",HEADER_MAP.get(PlatformHeaders.USER_ID.getValue()));

        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        IdentityContext result = multiTenantEntity.getIdentityContext();
        Assert.hasText(result.getUserId(), "userId is not empty");
        return ResponseEntity.success();
    }
    public ResponseEntity tc3_4() {
        try {
            requestOktaAllHeadersToUserId("wecom",NULL_STRING);
        }catch (Throwable e){
            return ResponseEntity.success();
        }
        return ResponseEntity.error("should throw exception");
    }

    public ResponseEntity tc3_5() {
        requestOktaAllHeadersToUserId(HEADER_MAP.get(PlatformHeaders.SOURCE.getValue()),HEADER_MAP.get(PlatformHeaders.USER_ID.getValue()));

        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        IdentityContext result = multiTenantEntity.getIdentityContext();
        Assert.hasText(result.getUserId(), "userId is not empty");
        return ResponseEntity.success();
    }

    public ResponseEntity tc3_6() {
        try {
            requestOktaAllHeadersToUserId(HEADER_MAP.get(PlatformHeaders.SOURCE.getValue()),NULL_STRING);
        }catch (Throwable e){
            return ResponseEntity.success();
        }
        return ResponseEntity.error("should throw exception");
    }

    public ResponseEntity tc3_7() {
        requestOktaAllHeadersToUserId(NULL_STRING,HEADER_MAP.get(PlatformHeaders.USER_ID.getValue()));

        MultiTenantEntity multiTenantEntity = MULTI_TENANT_ENTITY_MAP.get(ALL_PARAM_KEY);
        IdentityContext result = multiTenantEntity.getIdentityContext();
        Assert.hasText(result.getUserId(), "userId is not empty");
        return ResponseEntity.success();
    }

    public ResponseEntity tc3_8() {
        try {
            requestOktaAllHeadersToUserId(NULL_STRING,NULL_STRING);
        }catch (Throwable e){
            return ResponseEntity.success();
        }
        return ResponseEntity.error("should throw exception");
    }


    public ResponseEntity tc4_1() {
        try {
            requestUserIdThread(OKTA_ALL_HEADERS,ALL_PARAM_KEY);
        }catch(Throwable e){
            return ResponseEntity.error(e.getCause().getMessage());
        }
        return ResponseEntity.success();
    }


    public void requestOktaAllHeaders() {
        requestUrl(OKTA_ALL_HEADERS,ALL_PARAM_KEY);
    }

    /**
     * TENANT_ID,EMPLOYEE_EMAIL,CLIENT_ID No afferent
     */
    public void requestOktaPartHeaders() {
        requestUrl(OKTA_PART_HEADERS,PART_PARAM_KEY);
    }
    public void requestOktaEmptyHeaders() {
        Consumer<HttpHeaders> headersConsumer = header-> {};
        requestUrl(headersConsumer,EMPTY_PARAM_KEY);
    }

    public void requestOktaAllHeadersToEmail(String source,String loginName){
        Consumer<HttpHeaders> headersConsumer = header-> {
            header.set(PlatformHeaders.TENANT_ID.getValue(), HEADER_MAP.get(PlatformHeaders.TENANT_ID.getValue()));
            header.set(PlatformHeaders.CLIENT_ID.getValue(), HEADER_MAP.get(PlatformHeaders.CLIENT_ID.getValue()));
            header.set(PlatformHeaders.USER_ID.getValue(), HEADER_MAP.get(PlatformHeaders.USER_ID.getValue()));
            appendHeader(header);
            if(null != source){
                header.set(PlatformHeaders.SOURCE.getValue(), source);
            }
            if(null != loginName){
                header.set(PlatformHeaders.EMPLOYEE_EMAIL.getValue(), loginName);
            }
        };
        requestEmailUrl(headersConsumer,ALL_PARAM_KEY);
    }

    public void requestOktaAllHeadersToUserId(String source,String userId){
        Consumer<HttpHeaders> headersConsumer = header-> {
            header.set(PlatformHeaders.TENANT_ID.getValue(), HEADER_MAP.get(PlatformHeaders.TENANT_ID.getValue()));
            header.set(PlatformHeaders.CLIENT_ID.getValue(), HEADER_MAP.get(PlatformHeaders.CLIENT_ID.getValue()));
            header.set(PlatformHeaders.EMPLOYEE_EMAIL.getValue(), HEADER_MAP.get(PlatformHeaders.EMPLOYEE_EMAIL.getValue()));
            appendHeader(header);
            if(null != source){
                header.set(PlatformHeaders.SOURCE.getValue(), source);
            }
            if(null != userId){
                header.set(PlatformHeaders.USER_ID.getValue(), userId);
            }
        };
        requestUserIdUrl(headersConsumer,ALL_PARAM_KEY);
    }


    public void requestUrl(Consumer<HttpHeaders> headersConsumer,String sceneTypeKey){
        defaultClient.get().uri("/test/fullHeader?sceneTypeKey={sceneTypeKey}",sceneTypeKey).headers(headersConsumer).retrieve();
    }
    public void requestEmailUrl(Consumer<HttpHeaders> headersConsumer,String sceneTypeKey){
        defaultClient.get().uri("/test/emailHeader?sceneTypeKey={sceneTypeKey}", sceneTypeKey).headers(headersConsumer).retrieve().toEntity(ResponseEntity.class);
    }
    public void requestUserIdUrl(Consumer<HttpHeaders> headersConsumer,String sceneTypeKey){
        defaultClient.get().uri("/test/userIdHeader?sceneTypeKey={sceneTypeKey}",sceneTypeKey).headers(headersConsumer).retrieve().toEntity(ResponseEntity.class);
    }
    public void requestUserIdThread(Consumer<HttpHeaders> headersConsumer,String sceneTypeKey){
        defaultClient.get().uri("/test/threadHeader?sceneTypeKey={sceneTypeKey}",sceneTypeKey).headers(headersConsumer).retrieve().toEntity(ResponseEntity.class);
    }
}
