package com.kering.cus.qa.service;


import com.alibaba.fastjson.JSON;
import com.kering.cus.lib.common.context.GenericRequestContextHolder;
import com.kering.cus.qa.constants.CommunicationSdkConstants;
import com.kering.cus.qa.entity.ResponseEntity;
import com.kering.cus.qa.feign.CustomCommunicationClient;
import com.kering.cus.qa.feign.DefaultCommunicationClient;
import com.kering.cus.qa.feign.DefaultOpenFeignClient;
import com.kering.cus.qa.feign.NoneCommunicationClient;
import com.kering.cus.qa.utils.ThrowableUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CommunicationService {

    @Autowired
    private DefaultCommunicationClient defaultCommunicationClient;

    @Autowired
    private CustomCommunicationClient customCommunicationClient;

    @Autowired
    private NoneCommunicationClient noneCommunicationClient;

    @Autowired
    private DefaultOpenFeignClient defaultOpenFeignClient;


    private static final String SUCCESS = "200";

    public ResponseEntity tc001() {
        String result = defaultCommunicationClient.sendByteSize(0);
        Assert.isTrue(null != result, "Interface call failure");
        return ResponseEntity.success();
    }

    public ResponseEntity tc002() {
        String result = defaultCommunicationClient.sendByteSize(1);
        Assert.isTrue(null != result, "Interface call failure");
        return ResponseEntity.success();
    }

    public ResponseEntity tc003() {
        try {
            defaultCommunicationClient.sendByteSize(2);
        } catch (Throwable e) {
            return ResponseEntity.success(e.getCause().getMessage());
        }
        return ResponseEntity.error("should throw exception");
    }

    public ResponseEntity tc004() {
        try {
            defaultCommunicationClient.sendByteSize(3);
        } catch (Throwable e) {
            return ResponseEntity.success(e.getCause().getMessage());
        }
        return ResponseEntity.error("should throw exception");
    }


    public ResponseEntity tc005() {
        try {
            defaultCommunicationClient.sendByteSize(4);
        } catch (Throwable e) {
            return ResponseEntity.success(e.getCause().getMessage());
        }
        return ResponseEntity.error("should throw exception");
    }

    public ResponseEntity tc006() {
        String result = defaultCommunicationClient.sendByteSize(5);
        Assert.isTrue(null != result, "Interface call failure");
        return ResponseEntity.success();
    }

    public ResponseEntity tc007() {
        try {
            noneCommunicationClient.sendNoneUrl(CommunicationSdkConstants.BODY_LIST.get(0));
        } catch (Throwable e) {
            return ResponseEntity.success(e.getCause().getMessage());
        }
        return ResponseEntity.error("should throw exception");
    }

    public ResponseEntity tc008() {
        String result = defaultCommunicationClient.sendSleep(CommunicationSdkConstants.TIME_OUT_LIST.get(0));
        Assert.isTrue(SUCCESS.equals(result), "Interface call failure");
        return ResponseEntity.success();
    }

    public ResponseEntity tc009() {
        try {
            defaultCommunicationClient.sendSleep(CommunicationSdkConstants.TIME_OUT_LIST.get(1));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error("should throw exception");
    }

    public ResponseEntity tc010() {
        String result = customCommunicationClient.sendSleep(CommunicationSdkConstants.TIME_OUT_LIST.get(1));
        Assert.isTrue(SUCCESS.equals(result), "Interface call failure");
        return ResponseEntity.success();
    }

    public ResponseEntity tc011() {
        try {
            customCommunicationClient.sendSleep(CommunicationSdkConstants.TIME_OUT_LIST.get(2));
        } catch (Throwable e) {
            return ResponseEntity.success(ThrowableUtils.getStackTrace(e));
        }
        return ResponseEntity.error("should throw exception");
    }


    public ResponseEntity test012() {
        String result = defaultCommunicationClient.sendByteSize(0);
        Assert.isTrue(null != result, "Interface call failure");
        return ResponseEntity.success();
    }

    public ResponseEntity test013() {
        String result = defaultCommunicationClient.sendByteSize(0);
        Assert.isTrue(null != result, "Interface call failure");
        result = customCommunicationClient.sendSleep(CommunicationSdkConstants.TIME_OUT_LIST.get(1));
        Assert.isTrue(SUCCESS.equals(result), "Interface call failure");
        return ResponseEntity.success();
    }


    public ResponseEntity test014() {
        String result = defaultCommunicationClient.sendByteSize(0);
        Assert.isTrue(null != result, "Interface call failure");
        return ResponseEntity.success();
    }


    public ResponseEntity tc1_1() {
        Map<String, String> mapHeader = new HashMap<>();
        GenericRequestContextHolder.getRequestProperty().ifPresent(requestProperty ->
                mapHeader.putAll(JSON.parseObject(JSON.toJSONString(requestProperty.getRequestAttributeMaps()),Map.class))
        );
        Map<String, String> map = defaultOpenFeignClient.routeGetHeader();
        List<Map<String, String>> list = new ArrayList<>();
        list.add(mapHeader);
        list.add(map);
        return ResponseEntity.builder().data(list).isSuccess(true).build();
    }

    public ResponseEntity tc1_2() {
        Map<String, String> mapHeader = new HashMap<>();
        GenericRequestContextHolder.getRequestProperty().ifPresent(requestProperty ->
                mapHeader.putAll(JSON.parseObject(JSON.toJSONString(requestProperty.getRequestAttributeMaps()),Map.class))
        );
        Map<String, String> map = defaultCommunicationClient.routeGetHeader();
        List<Map<String, String>> list = new ArrayList<>();
        list.add(mapHeader);
        list.add(map);
        return ResponseEntity.builder().data(list).isSuccess(true).build();
    }

}
