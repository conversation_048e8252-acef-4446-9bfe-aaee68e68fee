package com.kering.cus.qa.feign;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpExchange(url = "http://qa.test.com/customCommunication/")
public interface NoneCommunicationClient {

    @PostExchange("/testRequest/none")
    String sendNoneUrl(@RequestBody String body);
}
