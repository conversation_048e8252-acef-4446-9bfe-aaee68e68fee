package com.kering.cus.qa.feign;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.Map;

@HttpExchange(url = "http://127.0.0.1:9100/defaultCommunication/")
public interface DefaultCommunicationClient {

    @PostExchange("/testRequest/size")
    String sendByteSize(@RequestBody Integer index);

    @PostExchange("/testRequest/none")
    String sendNoneUrl(@RequestBody String body);

    @PostExchange("/testRequest/sleep")
    String sendSleep(@RequestBody Long time);

    @PostExchange("/testRequest/getHeader")
    Map<String,String> routeGetHeader();
}
