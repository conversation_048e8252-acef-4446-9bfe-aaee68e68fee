package com.kering.cus.qa.feign;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpExchange(url = "http://127.0.0.1:9100/customCommunication/")
//@HttpExchangeConfig(configuration = CustomConfig.class)
public interface CustomCommunicationClient {

    @PostExchange("/testRequest/sleep")
    String sendSleep(@RequestBody Long time);
}
